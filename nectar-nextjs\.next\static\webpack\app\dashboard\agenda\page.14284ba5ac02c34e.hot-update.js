"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./node_modules/date-fns/parseISO.js":
/*!*******************************************!*\
  !*** ./node_modules/date-fns/parseISO.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   parseISO: () => (/* binding */ parseISO)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants.js */ \"(app-pages-browser)/./node_modules/date-fns/constants.js\");\n/* harmony import */ var _constructFrom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constructFrom.js */ \"(app-pages-browser)/./node_modules/date-fns/constructFrom.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./toDate.js */ \"(app-pages-browser)/./node_modules/date-fns/toDate.js\");\n\n\n\n/**\n * The {@link parseISO} function options.\n */ /**\n * @name parseISO\n * @category Common Helpers\n * @summary Parse ISO string\n *\n * @description\n * Parse the given string in ISO 8601 format and return an instance of Date.\n *\n * Function accepts complete ISO 8601 formats as well as partial implementations.\n * ISO 8601: http://en.wikipedia.org/wiki/ISO_8601\n *\n * If the argument isn't a string, the function cannot parse the string or\n * the values are invalid, it returns Invalid Date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param argument - The value to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Convert string '2014-02-11T11:30:30' to date:\n * const result = parseISO('2014-02-11T11:30:30')\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert string '+02014101' to date,\n * // if the additional number of digits in the extended year format is 1:\n * const result = parseISO('+02014101', { additionalDigits: 1 })\n * //=> Fri Apr 11 2014 00:00:00\n */ function parseISO(argument, options) {\n    const invalidDate = ()=>(0,_constructFrom_js__WEBPACK_IMPORTED_MODULE_0__.constructFrom)(options === null || options === void 0 ? void 0 : options.in, NaN);\n    var _options_additionalDigits;\n    const additionalDigits = (_options_additionalDigits = options === null || options === void 0 ? void 0 : options.additionalDigits) !== null && _options_additionalDigits !== void 0 ? _options_additionalDigits : 2;\n    const dateStrings = splitDateString(argument);\n    let date;\n    if (dateStrings.date) {\n        const parseYearResult = parseYear(dateStrings.date, additionalDigits);\n        date = parseDate(parseYearResult.restDateString, parseYearResult.year);\n    }\n    if (!date || isNaN(+date)) return invalidDate();\n    const timestamp = +date;\n    let time = 0;\n    let offset;\n    if (dateStrings.time) {\n        time = parseTime(dateStrings.time);\n        if (isNaN(time)) return invalidDate();\n    }\n    if (dateStrings.timezone) {\n        offset = parseTimezone(dateStrings.timezone);\n        if (isNaN(offset)) return invalidDate();\n    } else {\n        const tmpDate = new Date(timestamp + time);\n        const result = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_1__.toDate)(0, options === null || options === void 0 ? void 0 : options.in);\n        result.setFullYear(tmpDate.getUTCFullYear(), tmpDate.getUTCMonth(), tmpDate.getUTCDate());\n        result.setHours(tmpDate.getUTCHours(), tmpDate.getUTCMinutes(), tmpDate.getUTCSeconds(), tmpDate.getUTCMilliseconds());\n        return result;\n    }\n    return (0,_toDate_js__WEBPACK_IMPORTED_MODULE_1__.toDate)(timestamp + time + offset, options === null || options === void 0 ? void 0 : options.in);\n}\nconst patterns = {\n    dateTimeDelimiter: /[T ]/,\n    timeZoneDelimiter: /[Z ]/i,\n    timezone: /([Z+-].*)$/\n};\nconst dateRegex = /^-?(?:(\\d{3})|(\\d{2})(?:-?(\\d{2}))?|W(\\d{2})(?:-?(\\d{1}))?|)$/;\nconst timeRegex = /^(\\d{2}(?:[.,]\\d*)?)(?::?(\\d{2}(?:[.,]\\d*)?))?(?::?(\\d{2}(?:[.,]\\d*)?))?$/;\nconst timezoneRegex = /^([+-])(\\d{2})(?::?(\\d{2}))?$/;\nfunction splitDateString(dateString) {\n    const dateStrings = {};\n    const array = dateString.split(patterns.dateTimeDelimiter);\n    let timeString;\n    // The regex match should only return at maximum two array elements.\n    // [date], [time], or [date, time].\n    if (array.length > 2) {\n        return dateStrings;\n    }\n    if (/:/.test(array[0])) {\n        timeString = array[0];\n    } else {\n        dateStrings.date = array[0];\n        timeString = array[1];\n        if (patterns.timeZoneDelimiter.test(dateStrings.date)) {\n            dateStrings.date = dateString.split(patterns.timeZoneDelimiter)[0];\n            timeString = dateString.substr(dateStrings.date.length, dateString.length);\n        }\n    }\n    if (timeString) {\n        const token = patterns.timezone.exec(timeString);\n        if (token) {\n            dateStrings.time = timeString.replace(token[1], \"\");\n            dateStrings.timezone = token[1];\n        } else {\n            dateStrings.time = timeString;\n        }\n    }\n    return dateStrings;\n}\nfunction parseYear(dateString, additionalDigits) {\n    const regex = new RegExp(\"^(?:(\\\\d{4}|[+-]\\\\d{\" + (4 + additionalDigits) + \"})|(\\\\d{2}|[+-]\\\\d{\" + (2 + additionalDigits) + \"})$)\");\n    const captures = dateString.match(regex);\n    // Invalid ISO-formatted year\n    if (!captures) return {\n        year: NaN,\n        restDateString: \"\"\n    };\n    const year = captures[1] ? parseInt(captures[1]) : null;\n    const century = captures[2] ? parseInt(captures[2]) : null;\n    // either year or century is null, not both\n    return {\n        year: century === null ? year : century * 100,\n        restDateString: dateString.slice((captures[1] || captures[2]).length)\n    };\n}\nfunction parseDate(dateString, year) {\n    // Invalid ISO-formatted year\n    if (year === null) return new Date(NaN);\n    const captures = dateString.match(dateRegex);\n    // Invalid ISO-formatted string\n    if (!captures) return new Date(NaN);\n    const isWeekDate = !!captures[4];\n    const dayOfYear = parseDateUnit(captures[1]);\n    const month = parseDateUnit(captures[2]) - 1;\n    const day = parseDateUnit(captures[3]);\n    const week = parseDateUnit(captures[4]);\n    const dayOfWeek = parseDateUnit(captures[5]) - 1;\n    if (isWeekDate) {\n        if (!validateWeekDate(year, week, dayOfWeek)) {\n            return new Date(NaN);\n        }\n        return dayOfISOWeekYear(year, week, dayOfWeek);\n    } else {\n        const date = new Date(0);\n        if (!validateDate(year, month, day) || !validateDayOfYearDate(year, dayOfYear)) {\n            return new Date(NaN);\n        }\n        date.setUTCFullYear(year, month, Math.max(dayOfYear, day));\n        return date;\n    }\n}\nfunction parseDateUnit(value) {\n    return value ? parseInt(value) : 1;\n}\nfunction parseTime(timeString) {\n    const captures = timeString.match(timeRegex);\n    if (!captures) return NaN; // Invalid ISO-formatted time\n    const hours = parseTimeUnit(captures[1]);\n    const minutes = parseTimeUnit(captures[2]);\n    const seconds = parseTimeUnit(captures[3]);\n    if (!validateTime(hours, minutes, seconds)) {\n        return NaN;\n    }\n    return hours * _constants_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsInHour + minutes * _constants_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsInMinute + seconds * 1000;\n}\nfunction parseTimeUnit(value) {\n    return value && parseFloat(value.replace(\",\", \".\")) || 0;\n}\nfunction parseTimezone(timezoneString) {\n    if (timezoneString === \"Z\") return 0;\n    const captures = timezoneString.match(timezoneRegex);\n    if (!captures) return 0;\n    const sign = captures[1] === \"+\" ? -1 : 1;\n    const hours = parseInt(captures[2]);\n    const minutes = captures[3] && parseInt(captures[3]) || 0;\n    if (!validateTimezone(hours, minutes)) {\n        return NaN;\n    }\n    return sign * (hours * _constants_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsInHour + minutes * _constants_js__WEBPACK_IMPORTED_MODULE_2__.millisecondsInMinute);\n}\nfunction dayOfISOWeekYear(isoWeekYear, week, day) {\n    const date = new Date(0);\n    date.setUTCFullYear(isoWeekYear, 0, 4);\n    const fourthOfJanuaryDay = date.getUTCDay() || 7;\n    const diff = (week - 1) * 7 + day + 1 - fourthOfJanuaryDay;\n    date.setUTCDate(date.getUTCDate() + diff);\n    return date;\n}\n// Validation functions\n// February is null to handle the leap year (using ||)\nconst daysInMonths = [\n    31,\n    null,\n    31,\n    30,\n    31,\n    30,\n    31,\n    31,\n    30,\n    31,\n    30,\n    31\n];\nfunction isLeapYearIndex(year) {\n    return year % 400 === 0 || year % 4 === 0 && year % 100 !== 0;\n}\nfunction validateDate(year, month, date) {\n    return month >= 0 && month <= 11 && date >= 1 && date <= (daysInMonths[month] || (isLeapYearIndex(year) ? 29 : 28));\n}\nfunction validateDayOfYearDate(year, dayOfYear) {\n    return dayOfYear >= 1 && dayOfYear <= (isLeapYearIndex(year) ? 366 : 365);\n}\nfunction validateWeekDate(_year, week, day) {\n    return week >= 1 && week <= 53 && day >= 0 && day <= 6;\n}\nfunction validateTime(hours, minutes, seconds) {\n    if (hours === 24) {\n        return minutes === 0 && seconds === 0;\n    }\n    return seconds >= 0 && seconds < 60 && minutes >= 0 && minutes < 60 && hours >= 0 && hours < 25;\n}\nfunction validateTimezone(_hours, minutes) {\n    return minutes >= 0 && minutes <= 59;\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parseISO);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9wYXJzZUlTTy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUd3QjtBQUMyQjtBQUNkO0FBRXJDOztDQUVDLEdBRUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBZ0NDLEdBQ00sU0FBU0ksU0FBU0MsUUFBUSxFQUFFQyxPQUFPO0lBQ3hDLE1BQU1DLGNBQWMsSUFBTUwsZ0VBQWFBLENBQUNJLG9CQUFBQSw4QkFBQUEsUUFBU0UsRUFBRSxFQUFFQztRQUU1Qkg7SUFBekIsTUFBTUksbUJBQW1CSixDQUFBQSw0QkFBQUEsb0JBQUFBLDhCQUFBQSxRQUFTSSxnQkFBZ0IsY0FBekJKLHVDQUFBQSw0QkFBNkI7SUFDdEQsTUFBTUssY0FBY0MsZ0JBQWdCUDtJQUVwQyxJQUFJUTtJQUNKLElBQUlGLFlBQVlFLElBQUksRUFBRTtRQUNwQixNQUFNQyxrQkFBa0JDLFVBQVVKLFlBQVlFLElBQUksRUFBRUg7UUFDcERHLE9BQU9HLFVBQVVGLGdCQUFnQkcsY0FBYyxFQUFFSCxnQkFBZ0JJLElBQUk7SUFDdkU7SUFFQSxJQUFJLENBQUNMLFFBQVFNLE1BQU0sQ0FBQ04sT0FBTyxPQUFPTjtJQUVsQyxNQUFNYSxZQUFZLENBQUNQO0lBQ25CLElBQUlRLE9BQU87SUFDWCxJQUFJQztJQUVKLElBQUlYLFlBQVlVLElBQUksRUFBRTtRQUNwQkEsT0FBT0UsVUFBVVosWUFBWVUsSUFBSTtRQUNqQyxJQUFJRixNQUFNRSxPQUFPLE9BQU9kO0lBQzFCO0lBRUEsSUFBSUksWUFBWWEsUUFBUSxFQUFFO1FBQ3hCRixTQUFTRyxjQUFjZCxZQUFZYSxRQUFRO1FBQzNDLElBQUlMLE1BQU1HLFNBQVMsT0FBT2Y7SUFDNUIsT0FBTztRQUNMLE1BQU1tQixVQUFVLElBQUlDLEtBQUtQLFlBQVlDO1FBQ3JDLE1BQU1PLFNBQVN6QixrREFBTUEsQ0FBQyxHQUFHRyxvQkFBQUEsOEJBQUFBLFFBQVNFLEVBQUU7UUFDcENvQixPQUFPQyxXQUFXLENBQ2hCSCxRQUFRSSxjQUFjLElBQ3RCSixRQUFRSyxXQUFXLElBQ25CTCxRQUFRTSxVQUFVO1FBRXBCSixPQUFPSyxRQUFRLENBQ2JQLFFBQVFRLFdBQVcsSUFDbkJSLFFBQVFTLGFBQWEsSUFDckJULFFBQVFVLGFBQWEsSUFDckJWLFFBQVFXLGtCQUFrQjtRQUU1QixPQUFPVDtJQUNUO0lBRUEsT0FBT3pCLGtEQUFNQSxDQUFDaUIsWUFBWUMsT0FBT0MsUUFBUWhCLG9CQUFBQSw4QkFBQUEsUUFBU0UsRUFBRTtBQUN0RDtBQUVBLE1BQU04QixXQUFXO0lBQ2ZDLG1CQUFtQjtJQUNuQkMsbUJBQW1CO0lBQ25CaEIsVUFBVTtBQUNaO0FBRUEsTUFBTWlCLFlBQ0o7QUFDRixNQUFNQyxZQUNKO0FBQ0YsTUFBTUMsZ0JBQWdCO0FBRXRCLFNBQVMvQixnQkFBZ0JnQyxVQUFVO0lBQ2pDLE1BQU1qQyxjQUFjLENBQUM7SUFDckIsTUFBTWtDLFFBQVFELFdBQVdFLEtBQUssQ0FBQ1IsU0FBU0MsaUJBQWlCO0lBQ3pELElBQUlRO0lBRUosb0VBQW9FO0lBQ3BFLG1DQUFtQztJQUNuQyxJQUFJRixNQUFNRyxNQUFNLEdBQUcsR0FBRztRQUNwQixPQUFPckM7SUFDVDtJQUVBLElBQUksSUFBSXNDLElBQUksQ0FBQ0osS0FBSyxDQUFDLEVBQUUsR0FBRztRQUN0QkUsYUFBYUYsS0FBSyxDQUFDLEVBQUU7SUFDdkIsT0FBTztRQUNMbEMsWUFBWUUsSUFBSSxHQUFHZ0MsS0FBSyxDQUFDLEVBQUU7UUFDM0JFLGFBQWFGLEtBQUssQ0FBQyxFQUFFO1FBQ3JCLElBQUlQLFNBQVNFLGlCQUFpQixDQUFDUyxJQUFJLENBQUN0QyxZQUFZRSxJQUFJLEdBQUc7WUFDckRGLFlBQVlFLElBQUksR0FBRytCLFdBQVdFLEtBQUssQ0FBQ1IsU0FBU0UsaUJBQWlCLENBQUMsQ0FBQyxFQUFFO1lBQ2xFTyxhQUFhSCxXQUFXTSxNQUFNLENBQzVCdkMsWUFBWUUsSUFBSSxDQUFDbUMsTUFBTSxFQUN2QkosV0FBV0ksTUFBTTtRQUVyQjtJQUNGO0lBRUEsSUFBSUQsWUFBWTtRQUNkLE1BQU1JLFFBQVFiLFNBQVNkLFFBQVEsQ0FBQzRCLElBQUksQ0FBQ0w7UUFDckMsSUFBSUksT0FBTztZQUNUeEMsWUFBWVUsSUFBSSxHQUFHMEIsV0FBV00sT0FBTyxDQUFDRixLQUFLLENBQUMsRUFBRSxFQUFFO1lBQ2hEeEMsWUFBWWEsUUFBUSxHQUFHMkIsS0FBSyxDQUFDLEVBQUU7UUFDakMsT0FBTztZQUNMeEMsWUFBWVUsSUFBSSxHQUFHMEI7UUFDckI7SUFDRjtJQUVBLE9BQU9wQztBQUNUO0FBRUEsU0FBU0ksVUFBVTZCLFVBQVUsRUFBRWxDLGdCQUFnQjtJQUM3QyxNQUFNNEMsUUFBUSxJQUFJQyxPQUNoQix5QkFDRyxLQUFJN0MsZ0JBQWUsSUFDcEIsd0JBQ0MsS0FBSUEsZ0JBQWUsSUFDcEI7SUFHSixNQUFNOEMsV0FBV1osV0FBV2EsS0FBSyxDQUFDSDtJQUNsQyw2QkFBNkI7SUFDN0IsSUFBSSxDQUFDRSxVQUFVLE9BQU87UUFBRXRDLE1BQU1UO1FBQUtRLGdCQUFnQjtJQUFHO0lBRXRELE1BQU1DLE9BQU9zQyxRQUFRLENBQUMsRUFBRSxHQUFHRSxTQUFTRixRQUFRLENBQUMsRUFBRSxJQUFJO0lBQ25ELE1BQU1HLFVBQVVILFFBQVEsQ0FBQyxFQUFFLEdBQUdFLFNBQVNGLFFBQVEsQ0FBQyxFQUFFLElBQUk7SUFFdEQsMkNBQTJDO0lBQzNDLE9BQU87UUFDTHRDLE1BQU15QyxZQUFZLE9BQU96QyxPQUFPeUMsVUFBVTtRQUMxQzFDLGdCQUFnQjJCLFdBQVdnQixLQUFLLENBQUMsQ0FBQ0osUUFBUSxDQUFDLEVBQUUsSUFBSUEsUUFBUSxDQUFDLEVBQUUsRUFBRVIsTUFBTTtJQUN0RTtBQUNGO0FBRUEsU0FBU2hDLFVBQVU0QixVQUFVLEVBQUUxQixJQUFJO0lBQ2pDLDZCQUE2QjtJQUM3QixJQUFJQSxTQUFTLE1BQU0sT0FBTyxJQUFJUyxLQUFLbEI7SUFFbkMsTUFBTStDLFdBQVdaLFdBQVdhLEtBQUssQ0FBQ2hCO0lBQ2xDLCtCQUErQjtJQUMvQixJQUFJLENBQUNlLFVBQVUsT0FBTyxJQUFJN0IsS0FBS2xCO0lBRS9CLE1BQU1vRCxhQUFhLENBQUMsQ0FBQ0wsUUFBUSxDQUFDLEVBQUU7SUFDaEMsTUFBTU0sWUFBWUMsY0FBY1AsUUFBUSxDQUFDLEVBQUU7SUFDM0MsTUFBTVEsUUFBUUQsY0FBY1AsUUFBUSxDQUFDLEVBQUUsSUFBSTtJQUMzQyxNQUFNUyxNQUFNRixjQUFjUCxRQUFRLENBQUMsRUFBRTtJQUNyQyxNQUFNVSxPQUFPSCxjQUFjUCxRQUFRLENBQUMsRUFBRTtJQUN0QyxNQUFNVyxZQUFZSixjQUFjUCxRQUFRLENBQUMsRUFBRSxJQUFJO0lBRS9DLElBQUlLLFlBQVk7UUFDZCxJQUFJLENBQUNPLGlCQUFpQmxELE1BQU1nRCxNQUFNQyxZQUFZO1lBQzVDLE9BQU8sSUFBSXhDLEtBQUtsQjtRQUNsQjtRQUNBLE9BQU80RCxpQkFBaUJuRCxNQUFNZ0QsTUFBTUM7SUFDdEMsT0FBTztRQUNMLE1BQU10RCxPQUFPLElBQUljLEtBQUs7UUFDdEIsSUFDRSxDQUFDMkMsYUFBYXBELE1BQU04QyxPQUFPQyxRQUMzQixDQUFDTSxzQkFBc0JyRCxNQUFNNEMsWUFDN0I7WUFDQSxPQUFPLElBQUluQyxLQUFLbEI7UUFDbEI7UUFDQUksS0FBSzJELGNBQWMsQ0FBQ3RELE1BQU04QyxPQUFPUyxLQUFLQyxHQUFHLENBQUNaLFdBQVdHO1FBQ3JELE9BQU9wRDtJQUNUO0FBQ0Y7QUFFQSxTQUFTa0QsY0FBY1ksS0FBSztJQUMxQixPQUFPQSxRQUFRakIsU0FBU2lCLFNBQVM7QUFDbkM7QUFFQSxTQUFTcEQsVUFBVXdCLFVBQVU7SUFDM0IsTUFBTVMsV0FBV1QsV0FBV1UsS0FBSyxDQUFDZjtJQUNsQyxJQUFJLENBQUNjLFVBQVUsT0FBTy9DLEtBQUssNkJBQTZCO0lBRXhELE1BQU1tRSxRQUFRQyxjQUFjckIsUUFBUSxDQUFDLEVBQUU7SUFDdkMsTUFBTXNCLFVBQVVELGNBQWNyQixRQUFRLENBQUMsRUFBRTtJQUN6QyxNQUFNdUIsVUFBVUYsY0FBY3JCLFFBQVEsQ0FBQyxFQUFFO0lBRXpDLElBQUksQ0FBQ3dCLGFBQWFKLE9BQU9FLFNBQVNDLFVBQVU7UUFDMUMsT0FBT3RFO0lBQ1Q7SUFFQSxPQUNFbUUsUUFBUTVFLDZEQUFrQkEsR0FBRzhFLFVBQVU3RSwrREFBb0JBLEdBQUc4RSxVQUFVO0FBRTVFO0FBRUEsU0FBU0YsY0FBY0YsS0FBSztJQUMxQixPQUFPLFNBQVVNLFdBQVdOLE1BQU10QixPQUFPLENBQUMsS0FBSyxTQUFVO0FBQzNEO0FBRUEsU0FBUzVCLGNBQWN5RCxjQUFjO0lBQ25DLElBQUlBLG1CQUFtQixLQUFLLE9BQU87SUFFbkMsTUFBTTFCLFdBQVcwQixlQUFlekIsS0FBSyxDQUFDZDtJQUN0QyxJQUFJLENBQUNhLFVBQVUsT0FBTztJQUV0QixNQUFNMkIsT0FBTzNCLFFBQVEsQ0FBQyxFQUFFLEtBQUssTUFBTSxDQUFDLElBQUk7SUFDeEMsTUFBTW9CLFFBQVFsQixTQUFTRixRQUFRLENBQUMsRUFBRTtJQUNsQyxNQUFNc0IsVUFBVSxRQUFTLENBQUMsRUFBRSxJQUFJcEIsU0FBU0YsUUFBUSxDQUFDLEVBQUUsS0FBTTtJQUUxRCxJQUFJLENBQUM0QixpQkFBaUJSLE9BQU9FLFVBQVU7UUFDckMsT0FBT3JFO0lBQ1Q7SUFFQSxPQUFPMEUsT0FBUVAsQ0FBQUEsUUFBUTVFLDZEQUFrQkEsR0FBRzhFLFVBQVU3RSwrREFBbUI7QUFDM0U7QUFFQSxTQUFTb0UsaUJBQWlCZ0IsV0FBVyxFQUFFbkIsSUFBSSxFQUFFRCxHQUFHO0lBQzlDLE1BQU1wRCxPQUFPLElBQUljLEtBQUs7SUFDdEJkLEtBQUsyRCxjQUFjLENBQUNhLGFBQWEsR0FBRztJQUNwQyxNQUFNQyxxQkFBcUJ6RSxLQUFLMEUsU0FBUyxNQUFNO0lBQy9DLE1BQU1DLE9BQU8sQ0FBQ3RCLE9BQU8sS0FBSyxJQUFJRCxNQUFNLElBQUlxQjtJQUN4Q3pFLEtBQUs0RSxVQUFVLENBQUM1RSxLQUFLbUIsVUFBVSxLQUFLd0Q7SUFDcEMsT0FBTzNFO0FBQ1Q7QUFFQSx1QkFBdUI7QUFFdkIsc0RBQXNEO0FBQ3RELE1BQU02RSxlQUFlO0lBQUM7SUFBSTtJQUFNO0lBQUk7SUFBSTtJQUFJO0lBQUk7SUFBSTtJQUFJO0lBQUk7SUFBSTtJQUFJO0NBQUc7QUFFdkUsU0FBU0MsZ0JBQWdCekUsSUFBSTtJQUMzQixPQUFPQSxPQUFPLFFBQVEsS0FBTUEsT0FBTyxNQUFNLEtBQUtBLE9BQU8sUUFBUTtBQUMvRDtBQUVBLFNBQVNvRCxhQUFhcEQsSUFBSSxFQUFFOEMsS0FBSyxFQUFFbkQsSUFBSTtJQUNyQyxPQUNFbUQsU0FBUyxLQUNUQSxTQUFTLE1BQ1RuRCxRQUFRLEtBQ1JBLFFBQVM2RSxDQUFBQSxZQUFZLENBQUMxQixNQUFNLElBQUsyQixDQUFBQSxnQkFBZ0J6RSxRQUFRLEtBQUssRUFBQyxDQUFDO0FBRXBFO0FBRUEsU0FBU3FELHNCQUFzQnJELElBQUksRUFBRTRDLFNBQVM7SUFDNUMsT0FBT0EsYUFBYSxLQUFLQSxhQUFjNkIsQ0FBQUEsZ0JBQWdCekUsUUFBUSxNQUFNLEdBQUU7QUFDekU7QUFFQSxTQUFTa0QsaUJBQWlCd0IsS0FBSyxFQUFFMUIsSUFBSSxFQUFFRCxHQUFHO0lBQ3hDLE9BQU9DLFFBQVEsS0FBS0EsUUFBUSxNQUFNRCxPQUFPLEtBQUtBLE9BQU87QUFDdkQ7QUFFQSxTQUFTZSxhQUFhSixLQUFLLEVBQUVFLE9BQU8sRUFBRUMsT0FBTztJQUMzQyxJQUFJSCxVQUFVLElBQUk7UUFDaEIsT0FBT0UsWUFBWSxLQUFLQyxZQUFZO0lBQ3RDO0lBRUEsT0FDRUEsV0FBVyxLQUNYQSxVQUFVLE1BQ1ZELFdBQVcsS0FDWEEsVUFBVSxNQUNWRixTQUFTLEtBQ1RBLFFBQVE7QUFFWjtBQUVBLFNBQVNRLGlCQUFpQlMsTUFBTSxFQUFFZixPQUFPO0lBQ3ZDLE9BQU9BLFdBQVcsS0FBS0EsV0FBVztBQUNwQztBQUVBLG9DQUFvQztBQUNwQyxpRUFBZTFFLFFBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcY2lyb3ZcXERvY3VtZW50c1xcbmV4dC1qc1xcbmVjdGFyXFxuZWN0YXItbmV4dGpzXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxwYXJzZUlTTy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQge1xuICBtaWxsaXNlY29uZHNJbkhvdXIsXG4gIG1pbGxpc2Vjb25kc0luTWludXRlLFxufSBmcm9tIFwiLi9jb25zdGFudHMuanNcIjtcbmltcG9ydCB7IGNvbnN0cnVjdEZyb20gfSBmcm9tIFwiLi9jb25zdHJ1Y3RGcm9tLmpzXCI7XG5pbXBvcnQgeyB0b0RhdGUgfSBmcm9tIFwiLi90b0RhdGUuanNcIjtcblxuLyoqXG4gKiBUaGUge0BsaW5rIHBhcnNlSVNPfSBmdW5jdGlvbiBvcHRpb25zLlxuICovXG5cbi8qKlxuICogQG5hbWUgcGFyc2VJU09cbiAqIEBjYXRlZ29yeSBDb21tb24gSGVscGVyc1xuICogQHN1bW1hcnkgUGFyc2UgSVNPIHN0cmluZ1xuICpcbiAqIEBkZXNjcmlwdGlvblxuICogUGFyc2UgdGhlIGdpdmVuIHN0cmluZyBpbiBJU08gODYwMSBmb3JtYXQgYW5kIHJldHVybiBhbiBpbnN0YW5jZSBvZiBEYXRlLlxuICpcbiAqIEZ1bmN0aW9uIGFjY2VwdHMgY29tcGxldGUgSVNPIDg2MDEgZm9ybWF0cyBhcyB3ZWxsIGFzIHBhcnRpYWwgaW1wbGVtZW50YXRpb25zLlxuICogSVNPIDg2MDE6IGh0dHA6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvSVNPXzg2MDFcbiAqXG4gKiBJZiB0aGUgYXJndW1lbnQgaXNuJ3QgYSBzdHJpbmcsIHRoZSBmdW5jdGlvbiBjYW5ub3QgcGFyc2UgdGhlIHN0cmluZyBvclxuICogdGhlIHZhbHVlcyBhcmUgaW52YWxpZCwgaXQgcmV0dXJucyBJbnZhbGlkIERhdGUuXG4gKlxuICogQHR5cGVQYXJhbSBEYXRlVHlwZSAtIFRoZSBgRGF0ZWAgdHlwZSwgdGhlIGZ1bmN0aW9uIG9wZXJhdGVzIG9uLiBHZXRzIGluZmVycmVkIGZyb20gcGFzc2VkIGFyZ3VtZW50cy4gQWxsb3dzIHRvIHVzZSBleHRlbnNpb25zIGxpa2UgW2BVVENEYXRlYF0oaHR0cHM6Ly9naXRodWIuY29tL2RhdGUtZm5zL3V0YykuXG4gKiBAdHlwZVBhcmFtIFJlc3VsdERhdGUgLSBUaGUgcmVzdWx0IGBEYXRlYCB0eXBlLCBpdCBpcyB0aGUgdHlwZSByZXR1cm5lZCBmcm9tIHRoZSBjb250ZXh0IGZ1bmN0aW9uIGlmIGl0IGlzIHBhc3NlZCwgb3IgaW5mZXJyZWQgZnJvbSB0aGUgYXJndW1lbnRzLlxuICpcbiAqIEBwYXJhbSBhcmd1bWVudCAtIFRoZSB2YWx1ZSB0byBjb252ZXJ0XG4gKiBAcGFyYW0gb3B0aW9ucyAtIEFuIG9iamVjdCB3aXRoIG9wdGlvbnNcbiAqXG4gKiBAcmV0dXJucyBUaGUgcGFyc2VkIGRhdGUgaW4gdGhlIGxvY2FsIHRpbWUgem9uZVxuICpcbiAqIEBleGFtcGxlXG4gKiAvLyBDb252ZXJ0IHN0cmluZyAnMjAxNC0wMi0xMVQxMTozMDozMCcgdG8gZGF0ZTpcbiAqIGNvbnN0IHJlc3VsdCA9IHBhcnNlSVNPKCcyMDE0LTAyLTExVDExOjMwOjMwJylcbiAqIC8vPT4gVHVlIEZlYiAxMSAyMDE0IDExOjMwOjMwXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIENvbnZlcnQgc3RyaW5nICcrMDIwMTQxMDEnIHRvIGRhdGUsXG4gKiAvLyBpZiB0aGUgYWRkaXRpb25hbCBudW1iZXIgb2YgZGlnaXRzIGluIHRoZSBleHRlbmRlZCB5ZWFyIGZvcm1hdCBpcyAxOlxuICogY29uc3QgcmVzdWx0ID0gcGFyc2VJU08oJyswMjAxNDEwMScsIHsgYWRkaXRpb25hbERpZ2l0czogMSB9KVxuICogLy89PiBGcmkgQXByIDExIDIwMTQgMDA6MDA6MDBcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHBhcnNlSVNPKGFyZ3VtZW50LCBvcHRpb25zKSB7XG4gIGNvbnN0IGludmFsaWREYXRlID0gKCkgPT4gY29uc3RydWN0RnJvbShvcHRpb25zPy5pbiwgTmFOKTtcblxuICBjb25zdCBhZGRpdGlvbmFsRGlnaXRzID0gb3B0aW9ucz8uYWRkaXRpb25hbERpZ2l0cyA/PyAyO1xuICBjb25zdCBkYXRlU3RyaW5ncyA9IHNwbGl0RGF0ZVN0cmluZyhhcmd1bWVudCk7XG5cbiAgbGV0IGRhdGU7XG4gIGlmIChkYXRlU3RyaW5ncy5kYXRlKSB7XG4gICAgY29uc3QgcGFyc2VZZWFyUmVzdWx0ID0gcGFyc2VZZWFyKGRhdGVTdHJpbmdzLmRhdGUsIGFkZGl0aW9uYWxEaWdpdHMpO1xuICAgIGRhdGUgPSBwYXJzZURhdGUocGFyc2VZZWFyUmVzdWx0LnJlc3REYXRlU3RyaW5nLCBwYXJzZVllYXJSZXN1bHQueWVhcik7XG4gIH1cblxuICBpZiAoIWRhdGUgfHwgaXNOYU4oK2RhdGUpKSByZXR1cm4gaW52YWxpZERhdGUoKTtcblxuICBjb25zdCB0aW1lc3RhbXAgPSArZGF0ZTtcbiAgbGV0IHRpbWUgPSAwO1xuICBsZXQgb2Zmc2V0O1xuXG4gIGlmIChkYXRlU3RyaW5ncy50aW1lKSB7XG4gICAgdGltZSA9IHBhcnNlVGltZShkYXRlU3RyaW5ncy50aW1lKTtcbiAgICBpZiAoaXNOYU4odGltZSkpIHJldHVybiBpbnZhbGlkRGF0ZSgpO1xuICB9XG5cbiAgaWYgKGRhdGVTdHJpbmdzLnRpbWV6b25lKSB7XG4gICAgb2Zmc2V0ID0gcGFyc2VUaW1lem9uZShkYXRlU3RyaW5ncy50aW1lem9uZSk7XG4gICAgaWYgKGlzTmFOKG9mZnNldCkpIHJldHVybiBpbnZhbGlkRGF0ZSgpO1xuICB9IGVsc2Uge1xuICAgIGNvbnN0IHRtcERhdGUgPSBuZXcgRGF0ZSh0aW1lc3RhbXAgKyB0aW1lKTtcbiAgICBjb25zdCByZXN1bHQgPSB0b0RhdGUoMCwgb3B0aW9ucz8uaW4pO1xuICAgIHJlc3VsdC5zZXRGdWxsWWVhcihcbiAgICAgIHRtcERhdGUuZ2V0VVRDRnVsbFllYXIoKSxcbiAgICAgIHRtcERhdGUuZ2V0VVRDTW9udGgoKSxcbiAgICAgIHRtcERhdGUuZ2V0VVRDRGF0ZSgpLFxuICAgICk7XG4gICAgcmVzdWx0LnNldEhvdXJzKFxuICAgICAgdG1wRGF0ZS5nZXRVVENIb3VycygpLFxuICAgICAgdG1wRGF0ZS5nZXRVVENNaW51dGVzKCksXG4gICAgICB0bXBEYXRlLmdldFVUQ1NlY29uZHMoKSxcbiAgICAgIHRtcERhdGUuZ2V0VVRDTWlsbGlzZWNvbmRzKCksXG4gICAgKTtcbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG5cbiAgcmV0dXJuIHRvRGF0ZSh0aW1lc3RhbXAgKyB0aW1lICsgb2Zmc2V0LCBvcHRpb25zPy5pbik7XG59XG5cbmNvbnN0IHBhdHRlcm5zID0ge1xuICBkYXRlVGltZURlbGltaXRlcjogL1tUIF0vLFxuICB0aW1lWm9uZURlbGltaXRlcjogL1taIF0vaSxcbiAgdGltZXpvbmU6IC8oW1orLV0uKikkLyxcbn07XG5cbmNvbnN0IGRhdGVSZWdleCA9XG4gIC9eLT8oPzooXFxkezN9KXwoXFxkezJ9KSg/Oi0/KFxcZHsyfSkpP3xXKFxcZHsyfSkoPzotPyhcXGR7MX0pKT98KSQvO1xuY29uc3QgdGltZVJlZ2V4ID1cbiAgL14oXFxkezJ9KD86Wy4sXVxcZCopPykoPzo6PyhcXGR7Mn0oPzpbLixdXFxkKik/KSk/KD86Oj8oXFxkezJ9KD86Wy4sXVxcZCopPykpPyQvO1xuY29uc3QgdGltZXpvbmVSZWdleCA9IC9eKFsrLV0pKFxcZHsyfSkoPzo6PyhcXGR7Mn0pKT8kLztcblxuZnVuY3Rpb24gc3BsaXREYXRlU3RyaW5nKGRhdGVTdHJpbmcpIHtcbiAgY29uc3QgZGF0ZVN0cmluZ3MgPSB7fTtcbiAgY29uc3QgYXJyYXkgPSBkYXRlU3RyaW5nLnNwbGl0KHBhdHRlcm5zLmRhdGVUaW1lRGVsaW1pdGVyKTtcbiAgbGV0IHRpbWVTdHJpbmc7XG5cbiAgLy8gVGhlIHJlZ2V4IG1hdGNoIHNob3VsZCBvbmx5IHJldHVybiBhdCBtYXhpbXVtIHR3byBhcnJheSBlbGVtZW50cy5cbiAgLy8gW2RhdGVdLCBbdGltZV0sIG9yIFtkYXRlLCB0aW1lXS5cbiAgaWYgKGFycmF5Lmxlbmd0aCA+IDIpIHtcbiAgICByZXR1cm4gZGF0ZVN0cmluZ3M7XG4gIH1cblxuICBpZiAoLzovLnRlc3QoYXJyYXlbMF0pKSB7XG4gICAgdGltZVN0cmluZyA9IGFycmF5WzBdO1xuICB9IGVsc2Uge1xuICAgIGRhdGVTdHJpbmdzLmRhdGUgPSBhcnJheVswXTtcbiAgICB0aW1lU3RyaW5nID0gYXJyYXlbMV07XG4gICAgaWYgKHBhdHRlcm5zLnRpbWVab25lRGVsaW1pdGVyLnRlc3QoZGF0ZVN0cmluZ3MuZGF0ZSkpIHtcbiAgICAgIGRhdGVTdHJpbmdzLmRhdGUgPSBkYXRlU3RyaW5nLnNwbGl0KHBhdHRlcm5zLnRpbWVab25lRGVsaW1pdGVyKVswXTtcbiAgICAgIHRpbWVTdHJpbmcgPSBkYXRlU3RyaW5nLnN1YnN0cihcbiAgICAgICAgZGF0ZVN0cmluZ3MuZGF0ZS5sZW5ndGgsXG4gICAgICAgIGRhdGVTdHJpbmcubGVuZ3RoLFxuICAgICAgKTtcbiAgICB9XG4gIH1cblxuICBpZiAodGltZVN0cmluZykge1xuICAgIGNvbnN0IHRva2VuID0gcGF0dGVybnMudGltZXpvbmUuZXhlYyh0aW1lU3RyaW5nKTtcbiAgICBpZiAodG9rZW4pIHtcbiAgICAgIGRhdGVTdHJpbmdzLnRpbWUgPSB0aW1lU3RyaW5nLnJlcGxhY2UodG9rZW5bMV0sIFwiXCIpO1xuICAgICAgZGF0ZVN0cmluZ3MudGltZXpvbmUgPSB0b2tlblsxXTtcbiAgICB9IGVsc2Uge1xuICAgICAgZGF0ZVN0cmluZ3MudGltZSA9IHRpbWVTdHJpbmc7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIGRhdGVTdHJpbmdzO1xufVxuXG5mdW5jdGlvbiBwYXJzZVllYXIoZGF0ZVN0cmluZywgYWRkaXRpb25hbERpZ2l0cykge1xuICBjb25zdCByZWdleCA9IG5ldyBSZWdFeHAoXG4gICAgXCJeKD86KFxcXFxkezR9fFsrLV1cXFxcZHtcIiArXG4gICAgICAoNCArIGFkZGl0aW9uYWxEaWdpdHMpICtcbiAgICAgIFwifSl8KFxcXFxkezJ9fFsrLV1cXFxcZHtcIiArXG4gICAgICAoMiArIGFkZGl0aW9uYWxEaWdpdHMpICtcbiAgICAgIFwifSkkKVwiLFxuICApO1xuXG4gIGNvbnN0IGNhcHR1cmVzID0gZGF0ZVN0cmluZy5tYXRjaChyZWdleCk7XG4gIC8vIEludmFsaWQgSVNPLWZvcm1hdHRlZCB5ZWFyXG4gIGlmICghY2FwdHVyZXMpIHJldHVybiB7IHllYXI6IE5hTiwgcmVzdERhdGVTdHJpbmc6IFwiXCIgfTtcblxuICBjb25zdCB5ZWFyID0gY2FwdHVyZXNbMV0gPyBwYXJzZUludChjYXB0dXJlc1sxXSkgOiBudWxsO1xuICBjb25zdCBjZW50dXJ5ID0gY2FwdHVyZXNbMl0gPyBwYXJzZUludChjYXB0dXJlc1syXSkgOiBudWxsO1xuXG4gIC8vIGVpdGhlciB5ZWFyIG9yIGNlbnR1cnkgaXMgbnVsbCwgbm90IGJvdGhcbiAgcmV0dXJuIHtcbiAgICB5ZWFyOiBjZW50dXJ5ID09PSBudWxsID8geWVhciA6IGNlbnR1cnkgKiAxMDAsXG4gICAgcmVzdERhdGVTdHJpbmc6IGRhdGVTdHJpbmcuc2xpY2UoKGNhcHR1cmVzWzFdIHx8IGNhcHR1cmVzWzJdKS5sZW5ndGgpLFxuICB9O1xufVxuXG5mdW5jdGlvbiBwYXJzZURhdGUoZGF0ZVN0cmluZywgeWVhcikge1xuICAvLyBJbnZhbGlkIElTTy1mb3JtYXR0ZWQgeWVhclxuICBpZiAoeWVhciA9PT0gbnVsbCkgcmV0dXJuIG5ldyBEYXRlKE5hTik7XG5cbiAgY29uc3QgY2FwdHVyZXMgPSBkYXRlU3RyaW5nLm1hdGNoKGRhdGVSZWdleCk7XG4gIC8vIEludmFsaWQgSVNPLWZvcm1hdHRlZCBzdHJpbmdcbiAgaWYgKCFjYXB0dXJlcykgcmV0dXJuIG5ldyBEYXRlKE5hTik7XG5cbiAgY29uc3QgaXNXZWVrRGF0ZSA9ICEhY2FwdHVyZXNbNF07XG4gIGNvbnN0IGRheU9mWWVhciA9IHBhcnNlRGF0ZVVuaXQoY2FwdHVyZXNbMV0pO1xuICBjb25zdCBtb250aCA9IHBhcnNlRGF0ZVVuaXQoY2FwdHVyZXNbMl0pIC0gMTtcbiAgY29uc3QgZGF5ID0gcGFyc2VEYXRlVW5pdChjYXB0dXJlc1szXSk7XG4gIGNvbnN0IHdlZWsgPSBwYXJzZURhdGVVbml0KGNhcHR1cmVzWzRdKTtcbiAgY29uc3QgZGF5T2ZXZWVrID0gcGFyc2VEYXRlVW5pdChjYXB0dXJlc1s1XSkgLSAxO1xuXG4gIGlmIChpc1dlZWtEYXRlKSB7XG4gICAgaWYgKCF2YWxpZGF0ZVdlZWtEYXRlKHllYXIsIHdlZWssIGRheU9mV2VlaykpIHtcbiAgICAgIHJldHVybiBuZXcgRGF0ZShOYU4pO1xuICAgIH1cbiAgICByZXR1cm4gZGF5T2ZJU09XZWVrWWVhcih5ZWFyLCB3ZWVrLCBkYXlPZldlZWspO1xuICB9IGVsc2Uge1xuICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgwKTtcbiAgICBpZiAoXG4gICAgICAhdmFsaWRhdGVEYXRlKHllYXIsIG1vbnRoLCBkYXkpIHx8XG4gICAgICAhdmFsaWRhdGVEYXlPZlllYXJEYXRlKHllYXIsIGRheU9mWWVhcilcbiAgICApIHtcbiAgICAgIHJldHVybiBuZXcgRGF0ZShOYU4pO1xuICAgIH1cbiAgICBkYXRlLnNldFVUQ0Z1bGxZZWFyKHllYXIsIG1vbnRoLCBNYXRoLm1heChkYXlPZlllYXIsIGRheSkpO1xuICAgIHJldHVybiBkYXRlO1xuICB9XG59XG5cbmZ1bmN0aW9uIHBhcnNlRGF0ZVVuaXQodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlID8gcGFyc2VJbnQodmFsdWUpIDogMTtcbn1cblxuZnVuY3Rpb24gcGFyc2VUaW1lKHRpbWVTdHJpbmcpIHtcbiAgY29uc3QgY2FwdHVyZXMgPSB0aW1lU3RyaW5nLm1hdGNoKHRpbWVSZWdleCk7XG4gIGlmICghY2FwdHVyZXMpIHJldHVybiBOYU47IC8vIEludmFsaWQgSVNPLWZvcm1hdHRlZCB0aW1lXG5cbiAgY29uc3QgaG91cnMgPSBwYXJzZVRpbWVVbml0KGNhcHR1cmVzWzFdKTtcbiAgY29uc3QgbWludXRlcyA9IHBhcnNlVGltZVVuaXQoY2FwdHVyZXNbMl0pO1xuICBjb25zdCBzZWNvbmRzID0gcGFyc2VUaW1lVW5pdChjYXB0dXJlc1szXSk7XG5cbiAgaWYgKCF2YWxpZGF0ZVRpbWUoaG91cnMsIG1pbnV0ZXMsIHNlY29uZHMpKSB7XG4gICAgcmV0dXJuIE5hTjtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgaG91cnMgKiBtaWxsaXNlY29uZHNJbkhvdXIgKyBtaW51dGVzICogbWlsbGlzZWNvbmRzSW5NaW51dGUgKyBzZWNvbmRzICogMTAwMFxuICApO1xufVxuXG5mdW5jdGlvbiBwYXJzZVRpbWVVbml0KHZhbHVlKSB7XG4gIHJldHVybiAodmFsdWUgJiYgcGFyc2VGbG9hdCh2YWx1ZS5yZXBsYWNlKFwiLFwiLCBcIi5cIikpKSB8fCAwO1xufVxuXG5mdW5jdGlvbiBwYXJzZVRpbWV6b25lKHRpbWV6b25lU3RyaW5nKSB7XG4gIGlmICh0aW1lem9uZVN0cmluZyA9PT0gXCJaXCIpIHJldHVybiAwO1xuXG4gIGNvbnN0IGNhcHR1cmVzID0gdGltZXpvbmVTdHJpbmcubWF0Y2godGltZXpvbmVSZWdleCk7XG4gIGlmICghY2FwdHVyZXMpIHJldHVybiAwO1xuXG4gIGNvbnN0IHNpZ24gPSBjYXB0dXJlc1sxXSA9PT0gXCIrXCIgPyAtMSA6IDE7XG4gIGNvbnN0IGhvdXJzID0gcGFyc2VJbnQoY2FwdHVyZXNbMl0pO1xuICBjb25zdCBtaW51dGVzID0gKGNhcHR1cmVzWzNdICYmIHBhcnNlSW50KGNhcHR1cmVzWzNdKSkgfHwgMDtcblxuICBpZiAoIXZhbGlkYXRlVGltZXpvbmUoaG91cnMsIG1pbnV0ZXMpKSB7XG4gICAgcmV0dXJuIE5hTjtcbiAgfVxuXG4gIHJldHVybiBzaWduICogKGhvdXJzICogbWlsbGlzZWNvbmRzSW5Ib3VyICsgbWludXRlcyAqIG1pbGxpc2Vjb25kc0luTWludXRlKTtcbn1cblxuZnVuY3Rpb24gZGF5T2ZJU09XZWVrWWVhcihpc29XZWVrWWVhciwgd2VlaywgZGF5KSB7XG4gIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgwKTtcbiAgZGF0ZS5zZXRVVENGdWxsWWVhcihpc29XZWVrWWVhciwgMCwgNCk7XG4gIGNvbnN0IGZvdXJ0aE9mSmFudWFyeURheSA9IGRhdGUuZ2V0VVRDRGF5KCkgfHwgNztcbiAgY29uc3QgZGlmZiA9ICh3ZWVrIC0gMSkgKiA3ICsgZGF5ICsgMSAtIGZvdXJ0aE9mSmFudWFyeURheTtcbiAgZGF0ZS5zZXRVVENEYXRlKGRhdGUuZ2V0VVRDRGF0ZSgpICsgZGlmZik7XG4gIHJldHVybiBkYXRlO1xufVxuXG4vLyBWYWxpZGF0aW9uIGZ1bmN0aW9uc1xuXG4vLyBGZWJydWFyeSBpcyBudWxsIHRvIGhhbmRsZSB0aGUgbGVhcCB5ZWFyICh1c2luZyB8fClcbmNvbnN0IGRheXNJbk1vbnRocyA9IFszMSwgbnVsbCwgMzEsIDMwLCAzMSwgMzAsIDMxLCAzMSwgMzAsIDMxLCAzMCwgMzFdO1xuXG5mdW5jdGlvbiBpc0xlYXBZZWFySW5kZXgoeWVhcikge1xuICByZXR1cm4geWVhciAlIDQwMCA9PT0gMCB8fCAoeWVhciAlIDQgPT09IDAgJiYgeWVhciAlIDEwMCAhPT0gMCk7XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlRGF0ZSh5ZWFyLCBtb250aCwgZGF0ZSkge1xuICByZXR1cm4gKFxuICAgIG1vbnRoID49IDAgJiZcbiAgICBtb250aCA8PSAxMSAmJlxuICAgIGRhdGUgPj0gMSAmJlxuICAgIGRhdGUgPD0gKGRheXNJbk1vbnRoc1ttb250aF0gfHwgKGlzTGVhcFllYXJJbmRleCh5ZWFyKSA/IDI5IDogMjgpKVxuICApO1xufVxuXG5mdW5jdGlvbiB2YWxpZGF0ZURheU9mWWVhckRhdGUoeWVhciwgZGF5T2ZZZWFyKSB7XG4gIHJldHVybiBkYXlPZlllYXIgPj0gMSAmJiBkYXlPZlllYXIgPD0gKGlzTGVhcFllYXJJbmRleCh5ZWFyKSA/IDM2NiA6IDM2NSk7XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlV2Vla0RhdGUoX3llYXIsIHdlZWssIGRheSkge1xuICByZXR1cm4gd2VlayA+PSAxICYmIHdlZWsgPD0gNTMgJiYgZGF5ID49IDAgJiYgZGF5IDw9IDY7XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlVGltZShob3VycywgbWludXRlcywgc2Vjb25kcykge1xuICBpZiAoaG91cnMgPT09IDI0KSB7XG4gICAgcmV0dXJuIG1pbnV0ZXMgPT09IDAgJiYgc2Vjb25kcyA9PT0gMDtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgc2Vjb25kcyA+PSAwICYmXG4gICAgc2Vjb25kcyA8IDYwICYmXG4gICAgbWludXRlcyA+PSAwICYmXG4gICAgbWludXRlcyA8IDYwICYmXG4gICAgaG91cnMgPj0gMCAmJlxuICAgIGhvdXJzIDwgMjVcbiAgKTtcbn1cblxuZnVuY3Rpb24gdmFsaWRhdGVUaW1lem9uZShfaG91cnMsIG1pbnV0ZXMpIHtcbiAgcmV0dXJuIG1pbnV0ZXMgPj0gMCAmJiBtaW51dGVzIDw9IDU5O1xufVxuXG4vLyBGYWxsYmFjayBmb3IgbW9kdWxhcml6ZWQgaW1wb3J0czpcbmV4cG9ydCBkZWZhdWx0IHBhcnNlSVNPO1xuIl0sIm5hbWVzIjpbIm1pbGxpc2Vjb25kc0luSG91ciIsIm1pbGxpc2Vjb25kc0luTWludXRlIiwiY29uc3RydWN0RnJvbSIsInRvRGF0ZSIsInBhcnNlSVNPIiwiYXJndW1lbnQiLCJvcHRpb25zIiwiaW52YWxpZERhdGUiLCJpbiIsIk5hTiIsImFkZGl0aW9uYWxEaWdpdHMiLCJkYXRlU3RyaW5ncyIsInNwbGl0RGF0ZVN0cmluZyIsImRhdGUiLCJwYXJzZVllYXJSZXN1bHQiLCJwYXJzZVllYXIiLCJwYXJzZURhdGUiLCJyZXN0RGF0ZVN0cmluZyIsInllYXIiLCJpc05hTiIsInRpbWVzdGFtcCIsInRpbWUiLCJvZmZzZXQiLCJwYXJzZVRpbWUiLCJ0aW1lem9uZSIsInBhcnNlVGltZXpvbmUiLCJ0bXBEYXRlIiwiRGF0ZSIsInJlc3VsdCIsInNldEZ1bGxZZWFyIiwiZ2V0VVRDRnVsbFllYXIiLCJnZXRVVENNb250aCIsImdldFVUQ0RhdGUiLCJzZXRIb3VycyIsImdldFVUQ0hvdXJzIiwiZ2V0VVRDTWludXRlcyIsImdldFVUQ1NlY29uZHMiLCJnZXRVVENNaWxsaXNlY29uZHMiLCJwYXR0ZXJucyIsImRhdGVUaW1lRGVsaW1pdGVyIiwidGltZVpvbmVEZWxpbWl0ZXIiLCJkYXRlUmVnZXgiLCJ0aW1lUmVnZXgiLCJ0aW1lem9uZVJlZ2V4IiwiZGF0ZVN0cmluZyIsImFycmF5Iiwic3BsaXQiLCJ0aW1lU3RyaW5nIiwibGVuZ3RoIiwidGVzdCIsInN1YnN0ciIsInRva2VuIiwiZXhlYyIsInJlcGxhY2UiLCJyZWdleCIsIlJlZ0V4cCIsImNhcHR1cmVzIiwibWF0Y2giLCJwYXJzZUludCIsImNlbnR1cnkiLCJzbGljZSIsImlzV2Vla0RhdGUiLCJkYXlPZlllYXIiLCJwYXJzZURhdGVVbml0IiwibW9udGgiLCJkYXkiLCJ3ZWVrIiwiZGF5T2ZXZWVrIiwidmFsaWRhdGVXZWVrRGF0ZSIsImRheU9mSVNPV2Vla1llYXIiLCJ2YWxpZGF0ZURhdGUiLCJ2YWxpZGF0ZURheU9mWWVhckRhdGUiLCJzZXRVVENGdWxsWWVhciIsIk1hdGgiLCJtYXgiLCJ2YWx1ZSIsImhvdXJzIiwicGFyc2VUaW1lVW5pdCIsIm1pbnV0ZXMiLCJzZWNvbmRzIiwidmFsaWRhdGVUaW1lIiwicGFyc2VGbG9hdCIsInRpbWV6b25lU3RyaW5nIiwic2lnbiIsInZhbGlkYXRlVGltZXpvbmUiLCJpc29XZWVrWWVhciIsImZvdXJ0aE9mSmFudWFyeURheSIsImdldFVUQ0RheSIsImRpZmYiLCJzZXRVVENEYXRlIiwiZGF5c0luTW9udGhzIiwiaXNMZWFwWWVhckluZGV4IiwiX3llYXIiLCJfaG91cnMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/parseISO.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await fetch(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const data = await response.json();\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            initialData.start_time = selectInfo.start.toISOString().slice(0, 16);\n            initialData.end_time = selectInfo.end.toISOString().slice(0, 16);\n        } else if (selectedDate) {\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = startTime.toISOString().slice(0, 16);\n            initialData.end_time = endTime.toISOString().slice(0, 16);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // Handle appointment click - could open edit form\n        console.log('Appointment clicked:', appointment);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) throw new Error('Failed to create appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error creating appointment:', error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    appointmentCounts: appointmentCounts,\n                                                    clinicSettings: clinicSettings || undefined,\n                                                    appointments: allAppointments,\n                                                    className: \"rounded-md border-0 shadow-none w-full\",\n                                                    classNames: {\n                                                        months: \"flex flex-col space-y-4 w-full\",\n                                                        month: \"space-y-4 w-full\",\n                                                        caption: \"flex justify-center pt-1 relative items-center\",\n                                                        caption_label: \"text-sm font-medium\",\n                                                        nav: \"space-x-1 flex items-center\",\n                                                        nav_button: \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-accent rounded-md\",\n                                                        nav_button_previous: \"absolute left-1\",\n                                                        nav_button_next: \"absolute right-1\",\n                                                        table: \"w-full border-collapse space-y-1\",\n                                                        head_row: \"flex w-full\",\n                                                        head_cell: \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] text-center\",\n                                                        row: \"flex w-full mt-2\",\n                                                        cell: \"flex-1 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n                                                        day: \"h-9 w-full p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\",\n                                                        day_range_end: \"day-range-end\",\n                                                        day_selected: \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n                                                        day_today: \"bg-accent text-accent-foreground\",\n                                                        day_outside: \"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30\",\n                                                        day_disabled: \"text-muted-foreground opacity-50\",\n                                                        day_range_middle: \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n                                                        day_hidden: \"invisible\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors cursor-pointer space-y-3 sm:space-y-0\",\n                                                            onClick: ()=>handleAppointmentClick(appointment),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(appointment.start_time), 'HH:mm')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(new Date(appointment.end_time), 'HH:mm')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && 'Agendado',\n                                                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                                                appointment.status === 'completed' && 'Concluído',\n                                                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium whitespace-nowrap\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                appointment.price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: appointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"O/T8D1XACkCTyFGIp2fgJtnt9E4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2FnZW5kYS9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0M7QUFDNkM7QUFDakQ7QUFDRjtBQUNrQztBQUNLO0FBQ3hDO0FBQ1g7QUFFNEY7QUFDbEU7QUFDQztBQUNGO0FBZ0QzRCxNQUFNMEIsYUFBYTs7SUFDakIsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBRzNCLCtDQUFRQSxDQUFPLElBQUk0QjtJQUMzRCxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHOUIsK0NBQVFBLENBQWdCLEVBQUU7SUFDbEUsTUFBTSxDQUFDK0IsaUJBQWlCQyxtQkFBbUIsR0FBR2hDLCtDQUFRQSxDQUFnQixFQUFFO0lBQ3hFLE1BQU0sQ0FBQ2lDLFVBQVVDLFlBQVksR0FBR2xDLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDbUMseUJBQXlCQywyQkFBMkIsR0FBR3BDLCtDQUFRQSxDQUEyQixFQUFFO0lBQ25HLE1BQU0sQ0FBQ3FDLFlBQVlDLGNBQWMsR0FBR3RDLCtDQUFRQSxDQUFjLEVBQUU7SUFDNUQsTUFBTSxDQUFDdUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3hDLCtDQUFRQSxDQUF3QjtJQUM1RSxNQUFNLENBQUN5QyxTQUFTQyxXQUFXLEdBQUcxQywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMyQyxxQkFBcUJDLHVCQUF1QixHQUFHNUMsK0NBQVFBLENBQUM7SUFDL0QsTUFBTSxDQUFDNkMsYUFBYUMsZUFBZSxHQUFHOUMsK0NBQVFBLENBQThCO0lBQzVFLE1BQU0sQ0FBQytDLHFCQUFxQkMsdUJBQXVCLEdBQUdoRCwrQ0FBUUEsQ0FBTTtJQUNwRSxNQUFNLEVBQUVpRCxLQUFLLEVBQUUsR0FBRzlCLDBEQUFRQTtJQUUxQmxCLGdEQUFTQTtnQ0FBQztZQUNSaUQ7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7WUFDQUM7UUFDRjsrQkFBRztRQUFDN0I7S0FBYTtJQUVqQixNQUFNd0Isb0JBQW9CO1FBQ3hCLElBQUk7WUFDRlIsV0FBVztZQUNYLE1BQU1jLFVBQVVwQywrRUFBTUEsQ0FBQ00sY0FBYztZQUNyQyxNQUFNK0IsV0FBVyxNQUFNQyxNQUFNLDBCQUFrQyxPQUFSRjtZQUN2RCxJQUFJLENBQUNDLFNBQVNFLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTUMsT0FBTyxNQUFNSixTQUFTSyxJQUFJO1lBQ2hDaEMsZ0JBQWdCaUMsTUFBTUMsT0FBTyxDQUFDSCxRQUFRQSxPQUFPLEVBQUU7UUFDakQsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxnQ0FBZ0NBO1lBQzlDbkMsZ0JBQWdCLEVBQUU7WUFDbEJtQixNQUFNO2dCQUNKa0IsT0FBTztnQkFDUEMsYUFBYUgsaUJBQWlCTCxRQUFRSyxNQUFNSSxPQUFPLEdBQUc7Z0JBQ3REQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1I1QixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1VLGdCQUFnQjtRQUNwQixJQUFJO1lBQ0YsTUFBTUssV0FBVyxNQUFNbkMseUVBQXdCQSxDQUFDO1lBQ2hELElBQUksQ0FBQ21DLFNBQVNFLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTVcsU0FBUyxNQUFNZCxTQUFTSyxJQUFJO1lBQ2xDSSxRQUFRTSxHQUFHLENBQUMsMEJBQTBCRDtZQUN0QyxNQUFNVixPQUFPVSxPQUFPVixJQUFJLElBQUlVO1lBQzVCckMsWUFBWTZCLE1BQU1DLE9BQU8sQ0FBQ0gsUUFBUUEsT0FBTyxFQUFFO1FBQzdDLEVBQUUsT0FBT0ksT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQy9CLFlBQVksRUFBRTtZQUNkZSxNQUFNO2dCQUNKa0IsT0FBTztnQkFDUEMsYUFBYUgsaUJBQWlCTCxRQUFRSyxNQUFNSSxPQUFPLEdBQUc7Z0JBQ3REQyxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTWpCLCtCQUErQjtRQUNuQyxJQUFJO1lBQ0YsTUFBTUksV0FBVyxNQUFNbkMseUVBQXdCQSxDQUFDO1lBQ2hELElBQUksQ0FBQ21DLFNBQVNFLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTVcsU0FBUyxNQUFNZCxTQUFTSyxJQUFJO1lBQ2xDLE1BQU1ELE9BQU9VLE9BQU9WLElBQUksSUFBSVU7WUFDNUJuQywyQkFBMkIyQixNQUFNQyxPQUFPLENBQUNILFFBQVFBLE9BQU8sRUFBRTtRQUM1RCxFQUFFLE9BQU9JLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRDQUE0Q0E7WUFDMUQ3QiwyQkFBMkIsRUFBRTtRQUMvQjtJQUNGO0lBRUEsTUFBTWtCLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTUcsV0FBVyxNQUFNbkMseUVBQXdCQSxDQUFDO1lBQ2hELElBQUksQ0FBQ21DLFNBQVNFLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFDbEMsTUFBTVcsU0FBUyxNQUFNZCxTQUFTSyxJQUFJO1lBQ2xDLE1BQU1ELE9BQU9VLE9BQU9WLElBQUksSUFBSVU7WUFDNUJqQyxjQUFjeUIsTUFBTUMsT0FBTyxDQUFDSCxRQUFRQSxPQUFPLEVBQUU7UUFDL0MsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw4QkFBOEJBO1lBQzVDM0IsY0FBYyxFQUFFO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNYSx1QkFBdUI7UUFDM0IsSUFBSTtZQUNGLE1BQU1NLFdBQVcsTUFBTW5DLHlFQUF3QkEsQ0FBQztZQUNoRCxJQUFJLENBQUNtQyxTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBQ2xDLE1BQU1XLFNBQVMsTUFBTWQsU0FBU0ssSUFBSTtZQUNsQyxNQUFNRCxPQUFPVSxPQUFPVixJQUFJLElBQUlVO1lBQzVCdkMsbUJBQW1CK0IsTUFBTUMsT0FBTyxDQUFDSCxRQUFRQSxPQUFPLEVBQUU7UUFDcEQsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxvQ0FBb0NBO1lBQ2xEakMsbUJBQW1CLEVBQUU7UUFDdkI7SUFDRjtJQUVBLE1BQU11QixzQkFBc0I7UUFDMUIsSUFBSTtZQUNGLE1BQU1FLFdBQVcsTUFBTW5DLHlFQUF3QkEsQ0FBQztZQUNoRCxJQUFJLENBQUNtQyxTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBQ2xDLE1BQU1XLFNBQVMsTUFBTWQsU0FBU0ssSUFBSTtZQUNsQyxNQUFNRCxPQUFPVSxPQUFPVixJQUFJLElBQUlVO1lBQzVCL0Isa0JBQWtCcUI7UUFDcEIsRUFBRSxPQUFPSSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELHNDQUFzQztZQUN0Q3pCLGtCQUFrQjtnQkFDaEJpQyxxQkFBcUI7Z0JBQ3JCQyxtQkFBbUI7Z0JBQ25CQyxjQUFjO29CQUFDO29CQUFHO29CQUFHO29CQUFHO29CQUFHO2lCQUFFO2dCQUM3QkMsOEJBQThCO2dCQUM5QkMsNEJBQTRCO1lBQzlCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1DLDBCQUEwQixDQUFDQztRQUMvQixNQUFNQyxjQUFtQixDQUFDO1FBRTFCLElBQUlELFlBQVk7WUFDZEMsWUFBWUMsVUFBVSxHQUFHRixXQUFXRyxLQUFLLENBQUNDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLEdBQUc7WUFDakVKLFlBQVlLLFFBQVEsR0FBR04sV0FBV08sR0FBRyxDQUFDSCxXQUFXLEdBQUdDLEtBQUssQ0FBQyxHQUFHO1FBQy9ELE9BQU8sSUFBSTFELGNBQWM7WUFDdkIsTUFBTTZELFlBQVksSUFBSTNELEtBQUtGO1lBQzNCNkQsVUFBVUMsUUFBUSxDQUFDLEdBQUcsR0FBRyxHQUFHO1lBQzVCLE1BQU1DLFVBQVUsSUFBSTdELEtBQUsyRDtZQUN6QkUsUUFBUUMsVUFBVSxDQUFDRCxRQUFRRSxVQUFVLEtBQUs7WUFFMUNYLFlBQVlDLFVBQVUsR0FBR00sVUFBVUosV0FBVyxHQUFHQyxLQUFLLENBQUMsR0FBRztZQUMxREosWUFBWUssUUFBUSxHQUFHSSxRQUFRTixXQUFXLEdBQUdDLEtBQUssQ0FBQyxHQUFHO1FBQ3hEO1FBRUFwQyx1QkFBdUJnQztRQUN2QnBDLHVCQUF1QjtJQUN6QjtJQUVBLE1BQU1nRCx5QkFBeUIsQ0FBQ0M7UUFDOUIsa0RBQWtEO1FBQ2xEM0IsUUFBUU0sR0FBRyxDQUFDLHdCQUF3QnFCO0lBQ3RDO0lBRUEsTUFBTUMsMEJBQTBCLE9BQU9DLGVBQXVCQyxVQUFnQkM7UUFDNUUsSUFBSTtZQUNGLE1BQU14QyxXQUFXLE1BQU1uQyx5RUFBd0JBLENBQUMscUJBQW1DLE9BQWR5RSxnQkFBaUI7Z0JBQ3BGRyxRQUFRO2dCQUNSQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7b0JBQ25CckIsWUFBWWUsU0FBU2IsV0FBVztvQkFDaENFLFVBQVVZLE9BQU9kLFdBQVc7Z0JBQzlCO1lBQ0Y7WUFFQSxJQUFJLENBQUMxQixTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBRWxDLHVCQUF1QjtZQUN2QlY7UUFDRixFQUFFLE9BQU9lLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLCtCQUErQkE7WUFDN0MsTUFBTUE7UUFDUjtJQUNGO0lBRUEsTUFBTXNDLDBCQUEwQixPQUFPMUM7UUFDckMsSUFBSTtZQUNGLE1BQU1KLFdBQVcsTUFBTW5DLHlFQUF3QkEsQ0FBQyxxQkFBcUI7Z0JBQ25FNEUsUUFBUTtnQkFDUkMsU0FBUztvQkFBRSxnQkFBZ0I7Z0JBQW1CO2dCQUM5Q0MsTUFBTUMsS0FBS0MsU0FBUyxDQUFDekM7WUFDdkI7WUFFQSxJQUFJLENBQUNKLFNBQVNFLEVBQUUsRUFBRSxNQUFNLElBQUlDLE1BQU07WUFFbEMsdUJBQXVCO1lBQ3ZCVjtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQSx1REFBdUQ7SUFDdkQsTUFBTXVDLG9CQUFvQnpHLG9EQUFhO2lEQUFDO1lBQ3RDLE1BQU0yRyxTQUFpQyxDQUFDO1lBQ3hDN0UsYUFBYThFLE9BQU87eURBQUNkLENBQUFBO29CQUNuQixNQUFNZSxPQUFPLElBQUloRixLQUFLaUUsWUFBWVosVUFBVSxFQUFFRSxXQUFXLEdBQUcwQixLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3pFSCxNQUFNLENBQUNFLEtBQUssR0FBRyxDQUFDRixNQUFNLENBQUNFLEtBQUssSUFBSSxLQUFLO2dCQUN2Qzs7WUFDQSxPQUFPRjtRQUNUO2dEQUFHO1FBQUM3RTtLQUFhO0lBRWpCLE1BQU1pRiwwQkFBMEIsT0FBT2YsZUFBdUJnQjtRQUM1RCxJQUFJO1lBQ0YsTUFBTXRELFdBQVcsTUFBTW5DLHlFQUF3QkEsQ0FBQyxxQkFBbUMsT0FBZHlFLGdCQUFpQjtnQkFDcEZHLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtnQkFDOUNDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFBRVM7Z0JBQU87WUFDaEM7WUFFQSxJQUFJLENBQUN0RCxTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBRWxDWCxNQUFNO2dCQUNKa0IsT0FBTztnQkFDUEMsYUFBYTtZQUNmO1lBRUFsQjtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRGhCLE1BQU07Z0JBQ0prQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiRSxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEsTUFBTTBDLDBCQUEwQixPQUFPakI7UUFDckMsSUFBSSxDQUFDa0IsUUFBUSxrREFBa0Q7UUFFL0QsSUFBSTtZQUNGLE1BQU14RCxXQUFXLE1BQU1uQyx5RUFBd0JBLENBQUMscUJBQW1DLE9BQWR5RSxnQkFBaUI7Z0JBQ3BGRyxRQUFRO1lBQ1Y7WUFFQSxJQUFJLENBQUN6QyxTQUFTRSxFQUFFLEVBQUUsTUFBTSxJQUFJQyxNQUFNO1lBRWxDWCxNQUFNO2dCQUNKa0IsT0FBTztnQkFDUEMsYUFBYTtZQUNmO1lBRUFsQjtRQUNGLEVBQUUsT0FBT2UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3Q2hCLE1BQU07Z0JBQ0prQixPQUFPO2dCQUNQQyxhQUFhO2dCQUNiRSxTQUFTO1lBQ1g7UUFDRjtJQUNGO0lBRUEscUJBQ0UsOERBQUM0QztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDs7MENBQ0MsOERBQUNFO2dDQUFHRCxXQUFVOzBDQUFxQzs7Ozs7OzBDQUNuRCw4REFBQ0U7Z0NBQUVGLFdBQVU7MENBQXdCOzs7Ozs7Ozs7Ozs7a0NBR3ZDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQzNHLHlEQUFNQTs0QkFBQzhHLFNBQVMsSUFBTXhDOzs4Q0FDckIsOERBQUM5RCw4R0FBSUE7b0NBQUNtRyxXQUFVOzs7Ozs7Z0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXZDLDhEQUFDekcscURBQUlBO2dCQUNINkcsT0FBTzFFO2dCQUNQMkUsZUFBZSxDQUFDRDtvQkFDZCxnRUFBZ0U7b0JBQ2hFLElBQUk5RSxTQUFTO3dCQUNYUSxNQUFNOzRCQUNKa0IsT0FBTzs0QkFDUEMsYUFBYTs0QkFDYkUsU0FBUzt3QkFDWDt3QkFDQTtvQkFDRjtvQkFDQXhCLGVBQWV5RTtnQkFDakI7Z0JBQ0FKLFdBQVU7O2tDQUVWLDhEQUFDdkcseURBQVFBO3dCQUFDdUcsV0FBVTs7MENBQ2xCLDhEQUFDdEcsNERBQVdBO2dDQUNWMEcsT0FBTTtnQ0FDTkosV0FBVyw4Q0FBNkYsT0FBL0MxRSxVQUFVLGtDQUFrQztnQ0FDckdnRixVQUFVaEY7O2tEQUVWLDhEQUFDM0IsOEdBQVlBO3dDQUFDcUcsV0FBVTs7Ozs7O2tEQUN4Qiw4REFBQ087d0NBQUtQLFdBQVU7a0RBQW1COzs7Ozs7a0RBQ25DLDhEQUFDTzt3Q0FBS1AsV0FBVTtrREFBWTs7Ozs7O29DQUMzQjFFLHlCQUFXLDhEQUFDeUU7d0NBQUlDLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FFN0IsOERBQUN0Ryw0REFBV0E7Z0NBQ1YwRyxPQUFNO2dDQUNOSixXQUFXLDhDQUE2RixPQUEvQzFFLFVBQVUsa0NBQWtDO2dDQUNyR2dGLFVBQVVoRjs7a0RBRVYsOERBQUN2Qiw4R0FBT0E7d0NBQUNpRyxXQUFVOzs7Ozs7a0RBQ25CLDhEQUFDTzt3Q0FBS1AsV0FBVTtrREFBbUI7Ozs7OztrREFDbkMsOERBQUNPO3dDQUFLUCxXQUFVO2tEQUFZOzs7Ozs7b0NBQzNCMUUseUJBQVcsOERBQUN5RTt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUkvQiw4REFBQ3hHLDREQUFXQTt3QkFBQzRHLE9BQU07d0JBQVdKLFdBQVU7a0NBQ3RDLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNoSCxxREFBSUE7b0NBQUNnSCxXQUFVOztzREFDZCw4REFBQzdHLDJEQUFVQTtzREFDVCw0RUFBQ0MsMERBQVNBO2dEQUFDNEcsV0FBVTs7a0VBQ25CLDhEQUFDckcsOEdBQVlBO3dEQUFDcUcsV0FBVTs7Ozs7O29EQUE4Qjs7Ozs7Ozs7Ozs7O3NEQUkxRCw4REFBQy9HLDREQUFXQTs0Q0FBQytHLFdBQVU7c0RBQ3JCLDRFQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ2pILDZEQUFRQTtvREFDUHlILE1BQUs7b0RBQ0xDLFVBQVVsRztvREFDVm1HLFVBQVUsQ0FBQ2pCLE9BQVNBLFFBQVFqRixnQkFBZ0JpRjtvREFDNUNKLG1CQUFtQkE7b0RBQ25CakUsZ0JBQWdCQSxrQkFBa0J1RjtvREFDbENqRyxjQUFjRTtvREFDZG9GLFdBQVU7b0RBQ1ZZLFlBQVk7d0RBQ1ZDLFFBQVE7d0RBQ1JDLE9BQU87d0RBQ1BDLFNBQVM7d0RBQ1RDLGVBQWU7d0RBQ2ZDLEtBQUs7d0RBQ0xDLFlBQVk7d0RBQ1pDLHFCQUFxQjt3REFDckJDLGlCQUFpQjt3REFDakJDLE9BQU87d0RBQ1BDLFVBQVU7d0RBQ1ZDLFdBQVc7d0RBQ1hDLEtBQUs7d0RBQ0xDLE1BQU07d0RBQ05DLEtBQUs7d0RBQ0xDLGVBQWU7d0RBQ2ZDLGNBQWM7d0RBQ2RDLFdBQVc7d0RBQ1hDLGFBQWE7d0RBQ2JDLGNBQWM7d0RBQ2RDLGtCQUFrQjt3REFDbEJDLFlBQVk7b0RBQ2Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTVIsOERBQUNsQztvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ2hILHFEQUFJQTs7MERBQ0gsOERBQUNHLDJEQUFVQTs7a0VBQ1QsOERBQUNDLDBEQUFTQTt3REFBQzRHLFdBQVU7OzBFQUNuQiw4REFBQ3BHLDhHQUFLQTtnRUFBQ29HLFdBQVU7Ozs7OzswRUFDakIsOERBQUNPO2dFQUFLUCxXQUFVOztvRUFBbUI7b0VBQWE5Riw2REFBWUEsQ0FBQ0s7Ozs7Ozs7MEVBQzdELDhEQUFDZ0c7Z0VBQUtQLFdBQVU7MEVBQVk7Ozs7Ozs7Ozs7OztrRUFFOUIsOERBQUM5RyxnRUFBZUE7OzREQUNid0IsYUFBYXdILE1BQU07NERBQUM7Ozs7Ozs7Ozs7Ozs7MERBR3pCLDhEQUFDakosNERBQVdBOzBEQUNUcUMsd0JBQ0MsOERBQUN5RTtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNwRyw4R0FBS0E7NERBQUNvRyxXQUFVOzs7Ozs7c0VBQ2pCLDhEQUFDRTtzRUFBRTs7Ozs7Ozs7Ozs7Z0VBRUh4RixhQUFhd0gsTUFBTSxLQUFLLGtCQUMxQiw4REFBQ25DO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2xHLDhHQUFLQTs0REFBQ2tHLFdBQVU7Ozs7OztzRUFDakIsOERBQUNFO3NFQUFFOzs7Ozs7Ozs7Ozs4RUFHTCw4REFBQ0g7b0RBQUlDLFdBQVU7OERBQ1p0RixhQUFheUgsR0FBRyxDQUFDLENBQUN6RCw0QkFDakIsOERBQUNxQjs0REFFQ0MsV0FBVTs0REFDVkcsU0FBUyxJQUFNMUIsdUJBQXVCQzs7OEVBRXRDLDhEQUFDcUI7b0VBQUlDLFdBQVU7O3NGQUNiLDhEQUFDRDs0RUFBSUMsV0FBVTs7OEZBQ2IsOERBQUNPO29GQUFLUCxXQUFVOzhGQUNiL0YsK0VBQU1BLENBQUMsSUFBSVEsS0FBS2lFLFlBQVlaLFVBQVUsR0FBRzs7Ozs7OzhGQUU1Qyw4REFBQ3lDO29GQUFLUCxXQUFVOzhGQUNiL0YsK0VBQU1BLENBQUMsSUFBSVEsS0FBS2lFLFlBQVlSLFFBQVEsR0FBRzs7Ozs7Ozs7Ozs7O3NGQUc1Qyw4REFBQzZCOzRFQUFJQyxXQUFVOzs4RkFDYiw4REFBQ29DO29GQUFHcEMsV0FBVTs4RkFBd0J0QixZQUFZMUIsS0FBSzs7Ozs7OzhGQUN2RCw4REFBQ2tEO29GQUFFRixXQUFVOzhGQUNWdEIsWUFBWTJELFlBQVk7Ozs7OztnRkFFMUIzRCxZQUFZNEQsNEJBQTRCLGtCQUN2Qyw4REFBQ3BDO29GQUFFRixXQUFVOzhGQUNWdEIsWUFBWTRELDRCQUE0Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhFQUtqRCw4REFBQ3ZDO29FQUFJQyxXQUFVOztzRkFDYiw4REFBQzFHLHVEQUFLQTs0RUFDSjZELFNBQ0V1QixZQUFZa0IsTUFBTSxLQUFLLGNBQWMsWUFDckNsQixZQUFZa0IsTUFBTSxLQUFLLGNBQWMsY0FDckNsQixZQUFZa0IsTUFBTSxLQUFLLGNBQWMsZ0JBQ3JDOzRFQUVGSSxXQUFVOztnRkFFVHRCLFlBQVlrQixNQUFNLEtBQUssZUFBZTtnRkFDdENsQixZQUFZa0IsTUFBTSxLQUFLLGVBQWU7Z0ZBQ3RDbEIsWUFBWWtCLE1BQU0sS0FBSyxlQUFlO2dGQUN0Q2xCLFlBQVlrQixNQUFNLEtBQUssZUFBZTtnRkFDdENsQixZQUFZa0IsTUFBTSxLQUFLLGlCQUFpQjs7Ozs7Ozt3RUFFMUNsQixZQUFZNkQsS0FBSyxrQkFDaEIsOERBQUNoQzs0RUFBS1AsV0FBVTs7Z0ZBQXdDO2dGQUNsRHRCLFlBQVk2RCxLQUFLLENBQUNDLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7MkRBM0MvQjlELFlBQVkrRCxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQXlEckMsOERBQUNqSiw0REFBV0E7d0JBQUM0RyxPQUFNO3dCQUFlSixXQUFVO2tDQUMxQyw0RUFBQzVGLHFFQUFnQkE7NEJBQ2ZNLGNBQWNBOzRCQUNkTSx5QkFBeUJBOzRCQUN6QjBILHFCQUFxQi9FOzRCQUNyQmdGLG9CQUFvQmxFOzRCQUNwQm1FLHFCQUFxQmpFOzRCQUNyQnJELFNBQVNBOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLZiw4REFBQ2pCLG9FQUFlQTtnQkFDZHdJLE1BQU1ySDtnQkFDTnNILGNBQWNySDtnQkFDZFgsVUFBVUE7Z0JBQ1ZFLHlCQUF5QkE7Z0JBQ3pCRSxZQUFZQTtnQkFDWjJDLGFBQWFqQztnQkFDYm1ILFVBQVUzRDtnQkFDVjlELFNBQVNBOzs7Ozs7Ozs7Ozs7QUFJakI7R0ExY01oQjs7UUFZY04sc0RBQVFBOzs7S0FadEJNO0FBNGNOLGlFQUFlQSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGNpcm92XFxEb2N1bWVudHNcXG5leHQtanNcXG5lY3RhclxcbmVjdGFyLW5leHRqc1xcc3JjXFxhcHBcXGRhc2hib2FyZFxcYWdlbmRhXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENhbGVuZGFyIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhbGVuZGFyJztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJztcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90YWJzJztcbmltcG9ydCB7IENhbGVuZGFyIGFzIENhbGVuZGFySWNvbiwgQ2xvY2ssIFBsdXMsIFVzZXJzLCBHcmlkM1gzIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2UtdG9hc3QnO1xuaW1wb3J0IHsgZm9ybWF0IH0gZnJvbSAnZGF0ZS1mbnMnO1xuaW1wb3J0IHsgcHRCUiB9IGZyb20gJ2RhdGUtZm5zL2xvY2FsZSc7XG5pbXBvcnQgeyBmb3JtYXREYXRlQlIsIGZvcm1hdFRpbWVCUiwgZm9ybWF0RGF0ZVRpbWVCUiwgZ2V0QXBwb2ludG1lbnRTdGF0dXNCUiwgZ2V0QXBwb2ludG1lbnRUeXBlQlIgfSBmcm9tICdAL2xpYi9kYXRlLXV0aWxzJztcbmltcG9ydCB7IG1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCB9IGZyb20gJ0AvbGliL2FwaS1jbGllbnQnO1xuaW1wb3J0IEZ1bGxDYWxlbmRhclZpZXcgZnJvbSAnQC9jb21wb25lbnRzL0Z1bGxDYWxlbmRhclZpZXcnO1xuaW1wb3J0IEFwcG9pbnRtZW50Rm9ybSBmcm9tICdAL2NvbXBvbmVudHMvQXBwb2ludG1lbnRGb3JtJztcbmltcG9ydCB0eXBlIHsgRGF0ZVNlbGVjdEFyZyB9IGZyb20gJ0BmdWxsY2FsZW5kYXIvY29yZSc7XG5cbnR5cGUgUGF0aWVudCA9IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBlbWFpbD86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG59O1xuXG50eXBlIEhlYWx0aGNhcmVQcm9mZXNzaW9uYWwgPSB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgc3BlY2lhbHR5OiBzdHJpbmcgfCBudWxsO1xuICBpc19hY3RpdmU6IGJvb2xlYW47XG59O1xuXG50eXBlIFByb2NlZHVyZSA9IHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nIHwgbnVsbDtcbiAgZGVmYXVsdF9wcmljZTogbnVtYmVyIHwgbnVsbDtcbiAgZHVyYXRpb25fbWludXRlczogbnVtYmVyIHwgbnVsbDtcbn07XG5cbnR5cGUgQXBwb2ludG1lbnQgPSB7XG4gIGlkOiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmcgfCBudWxsO1xuICBwYXRpZW50X2lkOiBzdHJpbmc7XG4gIHBhdGllbnRfbmFtZT86IHN0cmluZztcbiAgaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfaWQ6IHN0cmluZyB8IG51bGw7XG4gIGhlYWx0aGNhcmVfcHJvZmVzc2lvbmFsX25hbWU/OiBzdHJpbmc7XG4gIHN0YXJ0X3RpbWU6IHN0cmluZztcbiAgZW5kX3RpbWU6IHN0cmluZztcbiAgdHlwZTogc3RyaW5nO1xuICBzdGF0dXM6IHN0cmluZztcbiAgcHJpY2U6IG51bWJlciB8IG51bGw7XG59O1xuXG50eXBlIENsaW5pY1NldHRpbmdzID0ge1xuICB3b3JraW5nX2hvdXJzX3N0YXJ0OiBzdHJpbmc7XG4gIHdvcmtpbmdfaG91cnNfZW5kOiBzdHJpbmc7XG4gIHdvcmtpbmdfZGF5czogbnVtYmVyW107XG4gIGFwcG9pbnRtZW50X2R1cmF0aW9uX21pbnV0ZXM6IG51bWJlcjtcbiAgYWxsb3dfd2Vla2VuZF9hcHBvaW50bWVudHM6IGJvb2xlYW47XG59O1xuXG5jb25zdCBBZ2VuZGFQYWdlID0gKCkgPT4ge1xuICBjb25zdCBbc2VsZWN0ZWREYXRlLCBzZXRTZWxlY3RlZERhdGVdID0gdXNlU3RhdGU8RGF0ZT4obmV3IERhdGUoKSk7XG4gIGNvbnN0IFthcHBvaW50bWVudHMsIHNldEFwcG9pbnRtZW50c10gPSB1c2VTdGF0ZTxBcHBvaW50bWVudFtdPihbXSk7XG4gIGNvbnN0IFthbGxBcHBvaW50bWVudHMsIHNldEFsbEFwcG9pbnRtZW50c10gPSB1c2VTdGF0ZTxBcHBvaW50bWVudFtdPihbXSk7XG4gIGNvbnN0IFtwYXRpZW50cywgc2V0UGF0aWVudHNdID0gdXNlU3RhdGU8UGF0aWVudFtdPihbXSk7XG4gIGNvbnN0IFtoZWFsdGhjYXJlUHJvZmVzc2lvbmFscywgc2V0SGVhbHRoY2FyZVByb2Zlc3Npb25hbHNdID0gdXNlU3RhdGU8SGVhbHRoY2FyZVByb2Zlc3Npb25hbFtdPihbXSk7XG4gIGNvbnN0IFtwcm9jZWR1cmVzLCBzZXRQcm9jZWR1cmVzXSA9IHVzZVN0YXRlPFByb2NlZHVyZVtdPihbXSk7XG4gIGNvbnN0IFtjbGluaWNTZXR0aW5ncywgc2V0Q2xpbmljU2V0dGluZ3NdID0gdXNlU3RhdGU8Q2xpbmljU2V0dGluZ3MgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFthcHBvaW50bWVudEZvcm1PcGVuLCBzZXRBcHBvaW50bWVudEZvcm1PcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2N1cnJlbnRWaWV3LCBzZXRDdXJyZW50Vmlld10gPSB1c2VTdGF0ZTwnY2FsZW5kYXInIHwgJ2Z1bGxjYWxlbmRhcic+KCdjYWxlbmRhcicpO1xuICBjb25zdCBbYXBwb2ludG1lbnRGb3JtRGF0YSwgc2V0QXBwb2ludG1lbnRGb3JtRGF0YV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpO1xuICBjb25zdCB7IHRvYXN0IH0gPSB1c2VUb2FzdCgpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hBcHBvaW50bWVudHMoKTtcbiAgICBmZXRjaEFsbEFwcG9pbnRtZW50cygpO1xuICAgIGZldGNoUGF0aWVudHMoKTtcbiAgICBmZXRjaEhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzKCk7XG4gICAgZmV0Y2hQcm9jZWR1cmVzKCk7XG4gICAgZmV0Y2hDbGluaWNTZXR0aW5ncygpO1xuICB9LCBbc2VsZWN0ZWREYXRlXSk7XG5cbiAgY29uc3QgZmV0Y2hBcHBvaW50bWVudHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBkYXRlU3RyID0gZm9ybWF0KHNlbGVjdGVkRGF0ZSwgJ3l5eXktTU0tZGQnKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvYXBwb2ludG1lbnRzP2RhdGU9JHtkYXRlU3RyfWApO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggYXBwb2ludG1lbnRzJyk7XG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0QXBwb2ludG1lbnRzKEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogW10pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBhcHBvaW50bWVudHM6JywgZXJyb3IpO1xuICAgICAgc2V0QXBwb2ludG1lbnRzKFtdKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRXJybyBhbyBjYXJyZWdhciBjb25zdWx0YXNcIixcbiAgICAgICAgZGVzY3JpcHRpb246IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ09jb3JyZXUgdW0gZXJybyBpbmVzcGVyYWRvJyxcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoUGF0aWVudHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvYXBpL3BhdGllbnRzJyk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBwYXRpZW50cycpO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc29sZS5sb2coJ1BhdGllbnRzIEFQSSByZXNwb25zZTonLCByZXN1bHQpO1xuICAgICAgY29uc3QgZGF0YSA9IHJlc3VsdC5kYXRhIHx8IHJlc3VsdDtcbiAgICAgIHNldFBhdGllbnRzKEFycmF5LmlzQXJyYXkoZGF0YSkgPyBkYXRhIDogW10pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBwYXRpZW50czonLCBlcnJvcik7XG4gICAgICBzZXRQYXRpZW50cyhbXSk7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcIkVycm8gYW8gY2FycmVnYXIgcGFjaWVudGVzXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdPY29ycmV1IHVtIGVycm8gaW5lc3BlcmFkbycsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoSGVhbHRoY2FyZVByb2Zlc3Npb25hbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvYXBpL2hlYWx0aGNhcmUtcHJvZmVzc2lvbmFscycpO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggaGVhbHRoY2FyZSBwcm9mZXNzaW9uYWxzJyk7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICBjb25zdCBkYXRhID0gcmVzdWx0LmRhdGEgfHwgcmVzdWx0O1xuICAgICAgc2V0SGVhbHRoY2FyZVByb2Zlc3Npb25hbHMoQXJyYXkuaXNBcnJheShkYXRhKSA/IGRhdGEgOiBbXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGhlYWx0aGNhcmUgcHJvZmVzc2lvbmFsczonLCBlcnJvcik7XG4gICAgICBzZXRIZWFsdGhjYXJlUHJvZmVzc2lvbmFscyhbXSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoUHJvY2VkdXJlcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoJy9hcGkvcHJvY2VkdXJlcycpO1xuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZmV0Y2ggcHJvY2VkdXJlcycpO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgY29uc3QgZGF0YSA9IHJlc3VsdC5kYXRhIHx8IHJlc3VsdDtcbiAgICAgIHNldFByb2NlZHVyZXMoQXJyYXkuaXNBcnJheShkYXRhKSA/IGRhdGEgOiBbXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHByb2NlZHVyZXM6JywgZXJyb3IpO1xuICAgICAgc2V0UHJvY2VkdXJlcyhbXSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGZldGNoQWxsQXBwb2ludG1lbnRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IG1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL2FwaS9hcHBvaW50bWVudHMnKTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGZldGNoIGFsbCBhcHBvaW50bWVudHMnKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnN0IGRhdGEgPSByZXN1bHQuZGF0YSB8fCByZXN1bHQ7XG4gICAgICBzZXRBbGxBcHBvaW50bWVudHMoQXJyYXkuaXNBcnJheShkYXRhKSA/IGRhdGEgOiBbXSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIGFsbCBhcHBvaW50bWVudHM6JywgZXJyb3IpO1xuICAgICAgc2V0QWxsQXBwb2ludG1lbnRzKFtdKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZmV0Y2hDbGluaWNTZXR0aW5ncyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoJy9hcGkvY2xpbmljLXNldHRpbmdzJyk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBmZXRjaCBjbGluaWMgc2V0dGluZ3MnKTtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnN0IGRhdGEgPSByZXN1bHQuZGF0YSB8fCByZXN1bHQ7XG4gICAgICBzZXRDbGluaWNTZXR0aW5ncyhkYXRhKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgY2xpbmljIHNldHRpbmdzOicsIGVycm9yKTtcbiAgICAgIC8vIFNldCBkZWZhdWx0IHNldHRpbmdzIGlmIGZldGNoIGZhaWxzXG4gICAgICBzZXRDbGluaWNTZXR0aW5ncyh7XG4gICAgICAgIHdvcmtpbmdfaG91cnNfc3RhcnQ6ICcwODowMCcsXG4gICAgICAgIHdvcmtpbmdfaG91cnNfZW5kOiAnMTg6MDAnLFxuICAgICAgICB3b3JraW5nX2RheXM6IFsxLCAyLCAzLCA0LCA1XSxcbiAgICAgICAgYXBwb2ludG1lbnRfZHVyYXRpb25fbWludXRlczogMzAsXG4gICAgICAgIGFsbG93X3dlZWtlbmRfYXBwb2ludG1lbnRzOiBmYWxzZVxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUFwcG9pbnRtZW50Q3JlYXRlID0gKHNlbGVjdEluZm8/OiBEYXRlU2VsZWN0QXJnKSA9PiB7XG4gICAgY29uc3QgaW5pdGlhbERhdGE6IGFueSA9IHt9O1xuXG4gICAgaWYgKHNlbGVjdEluZm8pIHtcbiAgICAgIGluaXRpYWxEYXRhLnN0YXJ0X3RpbWUgPSBzZWxlY3RJbmZvLnN0YXJ0LnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpO1xuICAgICAgaW5pdGlhbERhdGEuZW5kX3RpbWUgPSBzZWxlY3RJbmZvLmVuZC50b0lTT1N0cmluZygpLnNsaWNlKDAsIDE2KTtcbiAgICB9IGVsc2UgaWYgKHNlbGVjdGVkRGF0ZSkge1xuICAgICAgY29uc3Qgc3RhcnRUaW1lID0gbmV3IERhdGUoc2VsZWN0ZWREYXRlKTtcbiAgICAgIHN0YXJ0VGltZS5zZXRIb3Vycyg5LCAwLCAwLCAwKTtcbiAgICAgIGNvbnN0IGVuZFRpbWUgPSBuZXcgRGF0ZShzdGFydFRpbWUpO1xuICAgICAgZW5kVGltZS5zZXRNaW51dGVzKGVuZFRpbWUuZ2V0TWludXRlcygpICsgMzApO1xuXG4gICAgICBpbml0aWFsRGF0YS5zdGFydF90aW1lID0gc3RhcnRUaW1lLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpO1xuICAgICAgaW5pdGlhbERhdGEuZW5kX3RpbWUgPSBlbmRUaW1lLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTYpO1xuICAgIH1cblxuICAgIHNldEFwcG9pbnRtZW50Rm9ybURhdGEoaW5pdGlhbERhdGEpO1xuICAgIHNldEFwcG9pbnRtZW50Rm9ybU9wZW4odHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQXBwb2ludG1lbnRDbGljayA9IChhcHBvaW50bWVudDogQXBwb2ludG1lbnQpID0+IHtcbiAgICAvLyBIYW5kbGUgYXBwb2ludG1lbnQgY2xpY2sgLSBjb3VsZCBvcGVuIGVkaXQgZm9ybVxuICAgIGNvbnNvbGUubG9nKCdBcHBvaW50bWVudCBjbGlja2VkOicsIGFwcG9pbnRtZW50KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVBcHBvaW50bWVudFVwZGF0ZSA9IGFzeW5jIChhcHBvaW50bWVudElkOiBzdHJpbmcsIG5ld1N0YXJ0OiBEYXRlLCBuZXdFbmQ6IERhdGUpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoYC9hcGkvYXBwb2ludG1lbnRzLyR7YXBwb2ludG1lbnRJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgc3RhcnRfdGltZTogbmV3U3RhcnQudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICBlbmRfdGltZTogbmV3RW5kLnRvSVNPU3RyaW5nKCksXG4gICAgICAgIH0pXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIGFwcG9pbnRtZW50Jyk7XG5cbiAgICAgIC8vIFJlZnJlc2ggYXBwb2ludG1lbnRzXG4gICAgICBmZXRjaEFwcG9pbnRtZW50cygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBhcHBvaW50bWVudDonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQXBwb2ludG1lbnRTdWJtaXQgPSBhc3luYyAoZGF0YTogYW55KSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvYXBpL2FwcG9pbnRtZW50cycsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGNyZWF0ZSBhcHBvaW50bWVudCcpO1xuXG4gICAgICAvLyBSZWZyZXNoIGFwcG9pbnRtZW50c1xuICAgICAgZmV0Y2hBcHBvaW50bWVudHMoKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgYXBwb2ludG1lbnQ6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9O1xuXG4gIC8vIENhbGN1bGF0ZSBhcHBvaW50bWVudCBjb3VudHMgZm9yIGNhbGVuZGFyIGluZGljYXRvcnNcbiAgY29uc3QgYXBwb2ludG1lbnRDb3VudHMgPSBSZWFjdC51c2VNZW1vKCgpID0+IHtcbiAgICBjb25zdCBjb3VudHM6IFJlY29yZDxzdHJpbmcsIG51bWJlcj4gPSB7fTtcbiAgICBhcHBvaW50bWVudHMuZm9yRWFjaChhcHBvaW50bWVudCA9PiB7XG4gICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUoYXBwb2ludG1lbnQuc3RhcnRfdGltZSkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdO1xuICAgICAgY291bnRzW2RhdGVdID0gKGNvdW50c1tkYXRlXSB8fCAwKSArIDE7XG4gICAgfSk7XG4gICAgcmV0dXJuIGNvdW50cztcbiAgfSwgW2FwcG9pbnRtZW50c10pO1xuXG4gIGNvbnN0IHVwZGF0ZUFwcG9pbnRtZW50U3RhdHVzID0gYXN5bmMgKGFwcG9pbnRtZW50SWQ6IHN0cmluZywgc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBtYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoYC9hcGkvYXBwb2ludG1lbnRzLyR7YXBwb2ludG1lbnRJZH1gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICAgIGhlYWRlcnM6IHsgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHN0YXR1cyB9KVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIHVwZGF0ZSBhcHBvaW50bWVudCBzdGF0dXMnKTtcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJTdWNlc3NvIVwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTdGF0dXMgZGEgY29uc3VsdGEgYXR1YWxpemFkby5cIixcbiAgICAgIH0pO1xuXG4gICAgICBmZXRjaEFwcG9pbnRtZW50cygpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBhcHBvaW50bWVudCBzdGF0dXM6JywgZXJyb3IpO1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCJFcnJvXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkVycm8gYW8gYXR1YWxpemFyIHN0YXR1cyBkYSBjb25zdWx0YS5cIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiXG4gICAgICB9KTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlQXBwb2ludG1lbnQgPSBhc3luYyAoYXBwb2ludG1lbnRJZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKCdUZW0gY2VydGV6YSBxdWUgZGVzZWphIGV4Y2x1aXIgZXN0YSBjb25zdWx0YT8nKSkgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KGAvYXBpL2FwcG9pbnRtZW50cy8ke2FwcG9pbnRtZW50SWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIGFwcG9pbnRtZW50Jyk7XG5cbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiU3VjZXNzbyFcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwiQ29uc3VsdGEgZXhjbHXDrWRhIGNvbSBzdWNlc3NvLlwiLFxuICAgICAgfSk7XG5cbiAgICAgIGZldGNoQXBwb2ludG1lbnRzKCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIGFwcG9pbnRtZW50OicsIGVycm9yKTtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwiRXJyb1wiLFxuICAgICAgICBkZXNjcmlwdGlvbjogXCJFcnJvIGFvIGV4Y2x1aXIgY29uc3VsdGEuXCIsXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIlxuICAgICAgfSk7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWZvcmVncm91bmRcIj5BZ2VuZGE8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPkdlcmVuY2llIHN1YXMgY29uc3VsdGFzIGUgaG9yw6FyaW9zPC9wPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBoYW5kbGVBcHBvaW50bWVudENyZWF0ZSgpfT5cbiAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICBOb3ZhIENvbnN1bHRhXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxUYWJzXG4gICAgICAgIHZhbHVlPXtjdXJyZW50Vmlld31cbiAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB7XG4gICAgICAgICAgLy8gUHJldmVudCB0YWIgc3dpdGNoaW5nIGR1cmluZyBsb2FkaW5nIHRvIGF2b2lkIHJhY2UgY29uZGl0aW9uc1xuICAgICAgICAgIGlmIChsb2FkaW5nKSB7XG4gICAgICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgICAgIHRpdGxlOiBcIkFndWFyZGVcIixcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiQWd1YXJkZSBvIGNhcnJlZ2FtZW50byBkb3MgZGFkb3MgYW50ZXMgZGUgdHJvY2FyIGRlIGFiYS5cIixcbiAgICAgICAgICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICB9XG4gICAgICAgICAgc2V0Q3VycmVudFZpZXcodmFsdWUgYXMgJ2NhbGVuZGFyJyB8ICdmdWxsY2FsZW5kYXInKTtcbiAgICAgICAgfX1cbiAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcbiAgICAgID5cbiAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy0yXCI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyXG4gICAgICAgICAgICB2YWx1ZT1cImNhbGVuZGFyXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQteHMgc206dGV4dC1zbSAke2xvYWRpbmcgPyAnb3BhY2l0eS01MCBjdXJzb3Itbm90LWFsbG93ZWQnIDogJyd9YH1cbiAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxDYWxlbmRhckljb24gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJoaWRkZW4geHM6aW5saW5lXCI+Q2FsZW5kw6FyaW88L3NwYW4+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ4czpoaWRkZW5cIj5DYWwuPC9zcGFuPlxuICAgICAgICAgICAge2xvYWRpbmcgJiYgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMyB3LTMgYm9yZGVyLWItMiBib3JkZXItY3VycmVudCBtbC0xXCI+PC9kaXY+fVxuICAgICAgICAgIDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyXG4gICAgICAgICAgICB2YWx1ZT1cImZ1bGxjYWxlbmRhclwiXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXhzIHNtOnRleHQtc20gJHtsb2FkaW5nID8gJ29wYWNpdHktNTAgY3Vyc29yLW5vdC1hbGxvd2VkJyA6ICcnfWB9XG4gICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8R3JpZDNYMyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiB4czppbmxpbmVcIj5BZ2VuZGEgQ29tcGxldGE8L3NwYW4+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ4czpoaWRkZW5cIj5BZ2VuZGE8L3NwYW4+XG4gICAgICAgICAgICB7bG9hZGluZyAmJiA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zIHctMyBib3JkZXItYi0yIGJvcmRlci1jdXJyZW50IG1sLTFcIj48L2Rpdj59XG4gICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgPC9UYWJzTGlzdD5cblxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJjYWxlbmRhclwiIGNsYXNzTmFtZT1cInNwYWNlLXktNiBtdC02XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJsZzpjb2wtc3Bhbi0xXCI+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjbGFzc05hbWU9XCJtci0yIGgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgIENhbGVuZMOhcmlvXG4gICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPENhbGVuZGFyXG4gICAgICAgICAgICAgICAgICAgIG1vZGU9XCJzaW5nbGVcIlxuICAgICAgICAgICAgICAgICAgICBzZWxlY3RlZD17c2VsZWN0ZWREYXRlfVxuICAgICAgICAgICAgICAgICAgICBvblNlbGVjdD17KGRhdGUpID0+IGRhdGUgJiYgc2V0U2VsZWN0ZWREYXRlKGRhdGUpfVxuICAgICAgICAgICAgICAgICAgICBhcHBvaW50bWVudENvdW50cz17YXBwb2ludG1lbnRDb3VudHN9XG4gICAgICAgICAgICAgICAgICAgIGNsaW5pY1NldHRpbmdzPXtjbGluaWNTZXR0aW5ncyB8fCB1bmRlZmluZWR9XG4gICAgICAgICAgICAgICAgICAgIGFwcG9pbnRtZW50cz17YWxsQXBwb2ludG1lbnRzfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLW1kIGJvcmRlci0wIHNoYWRvdy1ub25lIHctZnVsbFwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZXM9e3tcbiAgICAgICAgICAgICAgICAgICAgICBtb250aHM6IFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTQgdy1mdWxsXCIsXG4gICAgICAgICAgICAgICAgICAgICAgbW9udGg6IFwic3BhY2UteS00IHctZnVsbFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGNhcHRpb246IFwiZmxleCBqdXN0aWZ5LWNlbnRlciBwdC0xIHJlbGF0aXZlIGl0ZW1zLWNlbnRlclwiLFxuICAgICAgICAgICAgICAgICAgICAgIGNhcHRpb25fbGFiZWw6IFwidGV4dC1zbSBmb250LW1lZGl1bVwiLFxuICAgICAgICAgICAgICAgICAgICAgIG5hdjogXCJzcGFjZS14LTEgZmxleCBpdGVtcy1jZW50ZXJcIixcbiAgICAgICAgICAgICAgICAgICAgICBuYXZfYnV0dG9uOiBcImgtNyB3LTcgYmctdHJhbnNwYXJlbnQgcC0wIG9wYWNpdHktNTAgaG92ZXI6b3BhY2l0eS0xMDAgaG92ZXI6YmctYWNjZW50IHJvdW5kZWQtbWRcIixcbiAgICAgICAgICAgICAgICAgICAgICBuYXZfYnV0dG9uX3ByZXZpb3VzOiBcImFic29sdXRlIGxlZnQtMVwiLFxuICAgICAgICAgICAgICAgICAgICAgIG5hdl9idXR0b25fbmV4dDogXCJhYnNvbHV0ZSByaWdodC0xXCIsXG4gICAgICAgICAgICAgICAgICAgICAgdGFibGU6IFwidy1mdWxsIGJvcmRlci1jb2xsYXBzZSBzcGFjZS15LTFcIixcbiAgICAgICAgICAgICAgICAgICAgICBoZWFkX3JvdzogXCJmbGV4IHctZnVsbFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGhlYWRfY2VsbDogXCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgcm91bmRlZC1tZCBmbGV4LTEgZm9udC1ub3JtYWwgdGV4dC1bMC44cmVtXSB0ZXh0LWNlbnRlclwiLFxuICAgICAgICAgICAgICAgICAgICAgIHJvdzogXCJmbGV4IHctZnVsbCBtdC0yXCIsXG4gICAgICAgICAgICAgICAgICAgICAgY2VsbDogXCJmbGV4LTEgdGV4dC1jZW50ZXIgdGV4dC1zbSBwLTAgcmVsYXRpdmUgWyY6aGFzKFthcmlhLXNlbGVjdGVkXS5kYXktcmFuZ2UtZW5kKV06cm91bmRlZC1yLW1kIFsmOmhhcyhbYXJpYS1zZWxlY3RlZF0uZGF5LW91dHNpZGUpXTpiZy1hY2NlbnQvNTAgWyY6aGFzKFthcmlhLXNlbGVjdGVkXSldOmJnLWFjY2VudCBmaXJzdDpbJjpoYXMoW2FyaWEtc2VsZWN0ZWRdKV06cm91bmRlZC1sLW1kIGxhc3Q6WyY6aGFzKFthcmlhLXNlbGVjdGVkXSldOnJvdW5kZWQtci1tZCBmb2N1cy13aXRoaW46cmVsYXRpdmUgZm9jdXMtd2l0aGluOnotMjBcIixcbiAgICAgICAgICAgICAgICAgICAgICBkYXk6IFwiaC05IHctZnVsbCBwLTAgZm9udC1ub3JtYWwgYXJpYS1zZWxlY3RlZDpvcGFjaXR5LTEwMCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRheV9yYW5nZV9lbmQ6IFwiZGF5LXJhbmdlLWVuZFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRheV9zZWxlY3RlZDogXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkgaG92ZXI6dGV4dC1wcmltYXJ5LWZvcmVncm91bmQgZm9jdXM6YmctcHJpbWFyeSBmb2N1czp0ZXh0LXByaW1hcnktZm9yZWdyb3VuZFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRheV90b2RheTogXCJiZy1hY2NlbnQgdGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRheV9vdXRzaWRlOiBcImRheS1vdXRzaWRlIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBvcGFjaXR5LTUwIGFyaWEtc2VsZWN0ZWQ6YmctYWNjZW50LzUwIGFyaWEtc2VsZWN0ZWQ6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGFyaWEtc2VsZWN0ZWQ6b3BhY2l0eS0zMFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRheV9kaXNhYmxlZDogXCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgb3BhY2l0eS01MFwiLFxuICAgICAgICAgICAgICAgICAgICAgIGRheV9yYW5nZV9taWRkbGU6IFwiYXJpYS1zZWxlY3RlZDpiZy1hY2NlbnQgYXJpYS1zZWxlY3RlZDp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgICAgICAgICAgICAgICAgZGF5X2hpZGRlbjogXCJpbnZpc2libGVcIixcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6Y29sLXNwYW4tMlwiPlxuICAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwibXItMiBoLTUgdy01IHRleHQtcHJpbWFyeVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImhpZGRlbiBzbTppbmxpbmVcIj5Db25zdWx0YXMgLSB7Zm9ybWF0RGF0ZUJSKHNlbGVjdGVkRGF0ZSl9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzbTpoaWRkZW5cIj5Db25zdWx0YXM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudHMubGVuZ3RofSBjb25zdWx0YShzKSBhZ2VuZGFkYShzKSBwYXJhIGVzdGUgZGlhXG4gICAgICAgICAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgbWItNCBvcGFjaXR5LTUwIGFuaW1hdGUtc3BpblwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPHA+Q2FycmVnYW5kbyBjb25zdWx0YXMuLi48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IGFwcG9pbnRtZW50cy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgbWItNCBvcGFjaXR5LTUwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8cD5OZW5odW1hIGNvbnN1bHRhIGFnZW5kYWRhIHBhcmEgZXN0ZSBkaWE8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnRzLm1hcCgoYXBwb2ludG1lbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXthcHBvaW50bWVudC5pZH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBzbTppdGVtcy1jZW50ZXIgc206anVzdGlmeS1iZXR3ZWVuIHAtNCByb3VuZGVkLWxnIGJvcmRlciBiZy1jYXJkLzUwIGhvdmVyOmJnLWNhcmQgdHJhbnNpdGlvbi1jb2xvcnMgY3Vyc29yLXBvaW50ZXIgc3BhY2UteS0zIHNtOnNwYWNlLXktMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUFwcG9pbnRtZW50Q2xpY2soYXBwb2ludG1lbnQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNCBtaW4tdy0wIGZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0KG5ldyBEYXRlKGFwcG9pbnRtZW50LnN0YXJ0X3RpbWUpLCAnSEg6bW0nKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXQobmV3IERhdGUoYXBwb2ludG1lbnQuZW5kX3RpbWUpLCAnSEg6bW0nKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdHJ1bmNhdGVcIj57YXBwb2ludG1lbnQudGl0bGV9PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudC5wYXRpZW50X25hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuaGVhbHRoY2FyZV9wcm9mZXNzaW9uYWxfbmFtZSAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIHRydW5jYXRlIGhpZGRlbiBzbTpibG9ja1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthcHBvaW50bWVudC5oZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGl0ZW1zLXN0YXJ0IHNtOml0ZW1zLWNlbnRlciBzcGFjZS15LTIgc206c3BhY2UteS0wIHNtOnNwYWNlLXgtMiBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXBwb2ludG1lbnQuc3RhdHVzID09PSAnY29uZmlybWVkJyA/ICdkZWZhdWx0JyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAnc2Vjb25kYXJ5JyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NhbmNlbGxlZCcgPyAnZGVzdHJ1Y3RpdmUnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJ291dGxpbmUnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuc3RhdHVzID09PSAnc2NoZWR1bGVkJyAmJiAnQWdlbmRhZG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbmZpcm1lZCcgJiYgJ0NvbmZpcm1hZG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgJiYgJ0NvbmNsdcOtZG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnN0YXR1cyA9PT0gJ2NhbmNlbGxlZCcgJiYgJ0NhbmNlbGFkbyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXBwb2ludG1lbnQuc3RhdHVzID09PSAnaW5fcHJvZ3Jlc3MnICYmICdFbSBBbmRhbWVudG8nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2FwcG9pbnRtZW50LnByaWNlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gd2hpdGVzcGFjZS1ub3dyYXBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUiQge2FwcG9pbnRtZW50LnByaWNlLnRvRml4ZWQoMil9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImZ1bGxjYWxlbmRhclwiIGNsYXNzTmFtZT1cInNwYWNlLXktNiBtdC02XCI+XG4gICAgICAgICAgPEZ1bGxDYWxlbmRhclZpZXdcbiAgICAgICAgICAgIGFwcG9pbnRtZW50cz17YXBwb2ludG1lbnRzfVxuICAgICAgICAgICAgaGVhbHRoY2FyZVByb2Zlc3Npb25hbHM9e2hlYWx0aGNhcmVQcm9mZXNzaW9uYWxzfVxuICAgICAgICAgICAgb25BcHBvaW50bWVudENyZWF0ZT17aGFuZGxlQXBwb2ludG1lbnRDcmVhdGV9XG4gICAgICAgICAgICBvbkFwcG9pbnRtZW50Q2xpY2s9e2hhbmRsZUFwcG9pbnRtZW50Q2xpY2t9XG4gICAgICAgICAgICBvbkFwcG9pbnRtZW50VXBkYXRlPXtoYW5kbGVBcHBvaW50bWVudFVwZGF0ZX1cbiAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cbiAgICAgIDwvVGFicz5cblxuICAgICAgPEFwcG9pbnRtZW50Rm9ybVxuICAgICAgICBvcGVuPXthcHBvaW50bWVudEZvcm1PcGVufVxuICAgICAgICBvbk9wZW5DaGFuZ2U9e3NldEFwcG9pbnRtZW50Rm9ybU9wZW59XG4gICAgICAgIHBhdGllbnRzPXtwYXRpZW50c31cbiAgICAgICAgaGVhbHRoY2FyZVByb2Zlc3Npb25hbHM9e2hlYWx0aGNhcmVQcm9mZXNzaW9uYWxzfVxuICAgICAgICBwcm9jZWR1cmVzPXtwcm9jZWR1cmVzfVxuICAgICAgICBpbml0aWFsRGF0YT17YXBwb2ludG1lbnRGb3JtRGF0YX1cbiAgICAgICAgb25TdWJtaXQ9e2hhbmRsZUFwcG9pbnRtZW50U3VibWl0fVxuICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEFnZW5kYVBhZ2U7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkNhbGVuZGFyIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIkJhZGdlIiwiVGFicyIsIlRhYnNDb250ZW50IiwiVGFic0xpc3QiLCJUYWJzVHJpZ2dlciIsIkNhbGVuZGFySWNvbiIsIkNsb2NrIiwiUGx1cyIsIlVzZXJzIiwiR3JpZDNYMyIsInVzZVRvYXN0IiwiZm9ybWF0IiwiZm9ybWF0RGF0ZUJSIiwibWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0IiwiRnVsbENhbGVuZGFyVmlldyIsIkFwcG9pbnRtZW50Rm9ybSIsIkFnZW5kYVBhZ2UiLCJzZWxlY3RlZERhdGUiLCJzZXRTZWxlY3RlZERhdGUiLCJEYXRlIiwiYXBwb2ludG1lbnRzIiwic2V0QXBwb2ludG1lbnRzIiwiYWxsQXBwb2ludG1lbnRzIiwic2V0QWxsQXBwb2ludG1lbnRzIiwicGF0aWVudHMiLCJzZXRQYXRpZW50cyIsImhlYWx0aGNhcmVQcm9mZXNzaW9uYWxzIiwic2V0SGVhbHRoY2FyZVByb2Zlc3Npb25hbHMiLCJwcm9jZWR1cmVzIiwic2V0UHJvY2VkdXJlcyIsImNsaW5pY1NldHRpbmdzIiwic2V0Q2xpbmljU2V0dGluZ3MiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImFwcG9pbnRtZW50Rm9ybU9wZW4iLCJzZXRBcHBvaW50bWVudEZvcm1PcGVuIiwiY3VycmVudFZpZXciLCJzZXRDdXJyZW50VmlldyIsImFwcG9pbnRtZW50Rm9ybURhdGEiLCJzZXRBcHBvaW50bWVudEZvcm1EYXRhIiwidG9hc3QiLCJmZXRjaEFwcG9pbnRtZW50cyIsImZldGNoQWxsQXBwb2ludG1lbnRzIiwiZmV0Y2hQYXRpZW50cyIsImZldGNoSGVhbHRoY2FyZVByb2Zlc3Npb25hbHMiLCJmZXRjaFByb2NlZHVyZXMiLCJmZXRjaENsaW5pY1NldHRpbmdzIiwiZGF0ZVN0ciIsInJlc3BvbnNlIiwiZmV0Y2giLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJBcnJheSIsImlzQXJyYXkiLCJlcnJvciIsImNvbnNvbGUiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwibWVzc2FnZSIsInZhcmlhbnQiLCJyZXN1bHQiLCJsb2ciLCJ3b3JraW5nX2hvdXJzX3N0YXJ0Iiwid29ya2luZ19ob3Vyc19lbmQiLCJ3b3JraW5nX2RheXMiLCJhcHBvaW50bWVudF9kdXJhdGlvbl9taW51dGVzIiwiYWxsb3dfd2Vla2VuZF9hcHBvaW50bWVudHMiLCJoYW5kbGVBcHBvaW50bWVudENyZWF0ZSIsInNlbGVjdEluZm8iLCJpbml0aWFsRGF0YSIsInN0YXJ0X3RpbWUiLCJzdGFydCIsInRvSVNPU3RyaW5nIiwic2xpY2UiLCJlbmRfdGltZSIsImVuZCIsInN0YXJ0VGltZSIsInNldEhvdXJzIiwiZW5kVGltZSIsInNldE1pbnV0ZXMiLCJnZXRNaW51dGVzIiwiaGFuZGxlQXBwb2ludG1lbnRDbGljayIsImFwcG9pbnRtZW50IiwiaGFuZGxlQXBwb2ludG1lbnRVcGRhdGUiLCJhcHBvaW50bWVudElkIiwibmV3U3RhcnQiLCJuZXdFbmQiLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJoYW5kbGVBcHBvaW50bWVudFN1Ym1pdCIsImFwcG9pbnRtZW50Q291bnRzIiwidXNlTWVtbyIsImNvdW50cyIsImZvckVhY2giLCJkYXRlIiwic3BsaXQiLCJ1cGRhdGVBcHBvaW50bWVudFN0YXR1cyIsInN0YXR1cyIsImhhbmRsZURlbGV0ZUFwcG9pbnRtZW50IiwiY29uZmlybSIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsIm9uQ2xpY2siLCJ2YWx1ZSIsIm9uVmFsdWVDaGFuZ2UiLCJkaXNhYmxlZCIsInNwYW4iLCJtb2RlIiwic2VsZWN0ZWQiLCJvblNlbGVjdCIsInVuZGVmaW5lZCIsImNsYXNzTmFtZXMiLCJtb250aHMiLCJtb250aCIsImNhcHRpb24iLCJjYXB0aW9uX2xhYmVsIiwibmF2IiwibmF2X2J1dHRvbiIsIm5hdl9idXR0b25fcHJldmlvdXMiLCJuYXZfYnV0dG9uX25leHQiLCJ0YWJsZSIsImhlYWRfcm93IiwiaGVhZF9jZWxsIiwicm93IiwiY2VsbCIsImRheSIsImRheV9yYW5nZV9lbmQiLCJkYXlfc2VsZWN0ZWQiLCJkYXlfdG9kYXkiLCJkYXlfb3V0c2lkZSIsImRheV9kaXNhYmxlZCIsImRheV9yYW5nZV9taWRkbGUiLCJkYXlfaGlkZGVuIiwibGVuZ3RoIiwibWFwIiwiaDMiLCJwYXRpZW50X25hbWUiLCJoZWFsdGhjYXJlX3Byb2Zlc3Npb25hbF9uYW1lIiwicHJpY2UiLCJ0b0ZpeGVkIiwiaWQiLCJvbkFwcG9pbnRtZW50Q3JlYXRlIiwib25BcHBvaW50bWVudENsaWNrIiwib25BcHBvaW50bWVudFVwZGF0ZSIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJvblN1Ym1pdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/date-utils.ts":
/*!*******************************!*\
  !*** ./src/lib/date-utils.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDateBR: () => (/* binding */ formatDateBR),\n/* harmony export */   formatDateForInput: () => (/* binding */ formatDateForInput),\n/* harmony export */   formatDateTimeBR: () => (/* binding */ formatDateTimeBR),\n/* harmony export */   formatDateTimeForInput: () => (/* binding */ formatDateTimeForInput),\n/* harmony export */   formatDateWithDayBR: () => (/* binding */ formatDateWithDayBR),\n/* harmony export */   formatTimeBR: () => (/* binding */ formatTimeBR),\n/* harmony export */   getAppointmentStatusBR: () => (/* binding */ getAppointmentStatusBR),\n/* harmony export */   getAppointmentTypeBR: () => (/* binding */ getAppointmentTypeBR),\n/* harmony export */   getRelativeTimeBR: () => (/* binding */ getRelativeTimeBR),\n/* harmony export */   parseBRDate: () => (/* binding */ parseBRDate)\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isValid.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n\n\n/**\n * Utility functions for Brazilian date and time formatting\n */ /**\n * Format date to Brazilian format (DD/MM/YYYY)\n */ function formatDateBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'dd/MM/yyyy', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Format time to Brazilian format (HH:mm)\n */ function formatTimeBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'HH:mm', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)\n */ function formatDateTimeBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'dd/MM/yyyy HH:mm', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)\n */ function formatDateWithDayBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'EEEE, dd/MM/yyyy', {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_3__.ptBR\n        });\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Format date for input fields (YYYY-MM-DD)\n */ function formatDateForInput(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'yyyy-MM-dd');\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Format datetime for input fields (YYYY-MM-DDTHH:mm)\n */ function formatDateTimeForInput(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, \"yyyy-MM-dd'T'HH:mm\");\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)\n */ function getRelativeTimeBR(date) {\n    try {\n        const dateObj = typeof date === 'string' ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_0__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(dateObj)) return '';\n        const today = new Date();\n        const tomorrow = new Date(today);\n        tomorrow.setDate(tomorrow.getDate() + 1);\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const dateStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(dateObj, 'yyyy-MM-dd');\n        const todayStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(today, 'yyyy-MM-dd');\n        const tomorrowStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(tomorrow, 'yyyy-MM-dd');\n        const yesterdayStr = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.format)(yesterday, 'yyyy-MM-dd');\n        if (dateStr === todayStr) return 'Hoje';\n        if (dateStr === tomorrowStr) return 'Amanhã';\n        if (dateStr === yesterdayStr) return 'Ontem';\n        return formatDateBR(dateObj);\n    } catch (e) {\n        return '';\n    }\n}\n/**\n * Parse Brazilian date format (DD/MM/YYYY) to Date object\n */ function parseBRDate(dateStr) {\n    try {\n        const parts = dateStr.split('/');\n        if (parts.length !== 3) return null;\n        const day = parseInt(parts[0], 10);\n        const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed\n        const year = parseInt(parts[2], 10);\n        const date = new Date(year, month, day);\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.isValid)(date)) return null;\n        return date;\n    } catch (e) {\n        return null;\n    }\n}\n/**\n * Get appointment status in Portuguese\n */ function getAppointmentStatusBR(status) {\n    const statusMap = {\n        'scheduled': 'Agendado',\n        'confirmed': 'Confirmado',\n        'in_progress': 'Em Andamento',\n        'completed': 'Concluído',\n        'cancelled': 'Cancelado',\n        'no_show': 'Faltou'\n    };\n    return statusMap[status] || status;\n}\n/**\n * Get appointment type in Portuguese\n */ function getAppointmentTypeBR(type) {\n    const typeMap = {\n        'consultation': 'Consulta',\n        'return': 'Retorno',\n        'teleconsultation': 'Teleconsulta',\n        'procedure': 'Procedimento',\n        'exam': 'Exame'\n    };\n    return typeMap[type] || type;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/date-utils.ts\n"));

/***/ })

});