import { useState, useEffect } from 'react'
import { makeAuthenticatedRequest } from '@/lib/api-client'
import type { Permission, Role } from '@/lib/permissions'

interface UsePermissionsReturn {
  permissions: Permission[]
  roles: string[]
  loading: boolean
  hasPermission: (resource: string, action: 'create' | 'read' | 'update' | 'delete') => boolean
  hasRole: (role: Role) => boolean
  isAdmin: boolean
  refetch: () => Promise<void>
}

export function usePermissions(): UsePermissionsReturn {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [roles, setRoles] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  const fetchPermissions = async () => {
    try {
      setLoading(true)
      
      // Fetch user roles
      const rolesResponse = await makeAuthenticatedRequest('/api/user-roles')
      if (rolesResponse.ok) {
        const rolesResult = await rolesResponse.json()
        const rolesData = rolesResult.data || rolesResult
        const userRoles = Array.isArray(rolesData) ? rolesData.map((r: any) => r.role_name) : []
        setRoles(userRoles)
      }

      // Fetch user permissions
      const permissionsResponse = await makeAuthenticatedRequest('/api/user-permissions')
      if (permissionsResponse.ok) {
        const permissionsResult = await permissionsResponse.json()
        const permissionsData = permissionsResult.data || permissionsResult
        setPermissions(Array.isArray(permissionsData) ? permissionsData : [])
      }
    } catch (error) {
      console.error('Error fetching permissions:', error)
      setPermissions([])
      setRoles([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPermissions()
  }, [])

  const hasPermission = (resource: string, action: 'create' | 'read' | 'update' | 'delete'): boolean => {
    return permissions.some(p => p.resource === resource && p.action === action)
  }

  const hasRole = (role: Role): boolean => {
    return roles.includes(role)
  }

  const isAdmin = hasRole('admin')

  return {
    permissions,
    roles,
    loading,
    hasPermission,
    hasRole,
    isAdmin,
    refetch: fetchPermissions
  }
}

// Hook for checking a specific permission
export function useHasPermission(resource: string, action: 'create' | 'read' | 'update' | 'delete'): {
  hasPermission: boolean
  loading: boolean
} {
  const { hasPermission, loading } = usePermissions()
  
  return {
    hasPermission: hasPermission(resource, action),
    loading
  }
}

// Hook for checking a specific role
export function useHasRole(role: Role): {
  hasRole: boolean
  loading: boolean
} {
  const { hasRole, loading } = usePermissions()
  
  return {
    hasRole: hasRole(role),
    loading
  }
}

// Component wrapper for conditional rendering based on permissions
interface PermissionGateProps {
  resource: string
  action: 'create' | 'read' | 'update' | 'delete'
  children: React.ReactNode
  fallback?: React.ReactNode
}

export function PermissionGate({ resource, action, children, fallback = null }: PermissionGateProps) {
  const { hasPermission, loading } = useHasPermission(resource, action)
  
  if (loading) {
    return <>{fallback}</>
  }
  
  return hasPermission ? <>{children}</> : <>{fallback}</>
}

// Component wrapper for conditional rendering based on roles
interface RoleGateProps {
  roles: Role[]
  children: React.ReactNode
  fallback?: React.ReactNode
  requireAll?: boolean // If true, user must have ALL roles. If false, user needs ANY role.
}

export function RoleGate({ roles, children, fallback = null, requireAll = false }: RoleGateProps) {
  const { hasRole, loading } = usePermissions()
  
  if (loading) {
    return <>{fallback}</>
  }
  
  const hasAccess = requireAll 
    ? roles.every(role => hasRole(role))
    : roles.some(role => hasRole(role))
  
  return hasAccess ? <>{children}</> : <>{fallback}</>
}
