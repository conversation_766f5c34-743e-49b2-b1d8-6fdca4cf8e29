import * as React from "react";
import { DayPicker } from "react-day-picker";
import { ptBR } from "date-fns/locale";
import { format, isAfter, isSameDay, startOfDay } from "date-fns";

import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

export type CalendarProps = React.ComponentProps<typeof DayPicker> & {
  appointmentCounts?: Record<string, number>;
  clinicSettings?: {
    working_hours_start: string;
    working_hours_end: string;
    working_days: number[];
    appointment_duration_minutes: number;
    allow_weekend_appointments: boolean;
  };
  appointments?: Array<{
    start_time: string;
    end_time: string;
  }>;
};

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  appointmentCounts = {},
  clinicSettings,
  appointments = [],
  ...props
}: CalendarProps) {
  const modifiers = React.useMemo(() => {
    const hasAppointments: Date[] = [];
    const hasAvailability: Date[] = [];
    const today = startOfDay(new Date());

    Object.keys(appointmentCounts).forEach(dateKey => {
      if (appointmentCounts[dateKey] > 0) {
        hasAppointments.push(new Date(dateKey));
      }
    });

    // Calculate availability for future dates only
    if (clinicSettings) {
      const calculateAvailableSlots = (date: Date) => {
        if (!isAfter(date, today) && !isSameDay(date, today)) return 0;

        const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7

        // Check if it's a working day
        if (!clinicSettings.working_days.includes(dayOfWeek)) {
          if (!clinicSettings.allow_weekend_appointments) return 0;
        }

        // Calculate total available slots
        const [startHour, startMinute] = clinicSettings.working_hours_start.split(':').map(Number);
        const [endHour, endMinute] = clinicSettings.working_hours_end.split(':').map(Number);

        const startMinutes = startHour * 60 + startMinute;
        const endMinutes = endHour * 60 + endMinute;
        const totalMinutes = endMinutes - startMinutes;
        const totalSlots = Math.floor(totalMinutes / clinicSettings.appointment_duration_minutes);

        // Count existing appointments for this date
        const dateStr = format(date, 'yyyy-MM-dd');
        const dayAppointments = appointments.filter(apt =>
          format(new Date(apt.start_time), 'yyyy-MM-dd') === dateStr
        );

        const occupiedSlots = dayAppointments.length;
        return Math.max(0, totalSlots - occupiedSlots);
      };

      // Check availability for the next 60 days
      for (let i = 0; i < 60; i++) {
        const checkDate = new Date(today);
        checkDate.setDate(checkDate.getDate() + i);

        if (calculateAvailableSlots(checkDate) > 0) {
          hasAvailability.push(checkDate);
        }
      }
    }

    return { hasAppointments, hasAvailability };
  }, [appointmentCounts, clinicSettings, appointments]);

  const modifiersClassNames = {
    hasAppointments: "relative after:absolute after:bottom-1 after:left-1/2 after:transform after:-translate-x-1/2 after:w-1 after:h-1 after:bg-primary after:rounded-full",
    hasAvailability: "bg-green-50 hover:bg-green-100 border-green-200"
  };

  return (
    <DayPicker
      locale={ptBR}
      showOutsideDays={showOutsideDays}
      className={cn("p-3", className)}
      modifiers={modifiers}
      modifiersClassNames={modifiersClassNames}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center",
        caption_label: "text-sm font-medium",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          buttonVariants({ variant: "outline" }),
          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex",
        head_cell:
          "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
        row: "flex w-full mt-2",
        cell: "h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-9 w-9 p-0 font-normal aria-selected:opacity-100"
        ),
        day_range_end: "day-range-end",
        day_selected:
          "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
        day_today: "bg-accent text-accent-foreground",
        day_outside:
          "day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
        day_disabled: "text-muted-foreground opacity-50",
        day_range_middle:
          "aria-selected:bg-accent aria-selected:text-accent-foreground",
        day_hidden: "invisible",
        ...classNames,
      }}
      {...props}
    />
  );
}
Calendar.displayName = "Calendar";

export { Calendar };
