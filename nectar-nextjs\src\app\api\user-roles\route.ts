import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type UserRole = Tables<'user_roles'>
type UserRoleInsert = TablesInsert<'user_roles'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { data: roles, error } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(roles || [])
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      
      const roleData: UserRoleInsert = {
        user_id: userId,
        role_name: body.role_name
      }

      const { data: role, error } = await supabase
        .from('user_roles')
        .insert(roleData)
        .select()
        .single()

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(role, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
