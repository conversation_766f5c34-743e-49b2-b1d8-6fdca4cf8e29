"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await fetch(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const data = await response.json();\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            initialData.start_time = selectInfo.start.toISOString().slice(0, 16);\n            initialData.end_time = selectInfo.end.toISOString().slice(0, 16);\n        } else if (selectedDate) {\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = startTime.toISOString().slice(0, 16);\n            initialData.end_time = endTime.toISOString().slice(0, 16);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // Handle appointment click - could open edit form\n        console.log('Appointment clicked:', appointment);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) throw new Error('Failed to create appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error creating appointment:', error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_9__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    appointmentCounts: appointmentCounts,\n                                                    clinicSettings: clinicSettings || undefined,\n                                                    appointments: allAppointments,\n                                                    className: \"rounded-md border-0 shadow-none w-full\",\n                                                    classNames: {\n                                                        months: \"flex flex-col space-y-4 w-full\",\n                                                        month: \"space-y-4 w-full\",\n                                                        caption: \"flex justify-center pt-1 relative items-center\",\n                                                        caption_label: \"text-sm font-medium\",\n                                                        nav: \"space-x-1 flex items-center\",\n                                                        nav_button: \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-accent rounded-md\",\n                                                        nav_button_previous: \"absolute left-1\",\n                                                        nav_button_next: \"absolute right-1\",\n                                                        table: \"w-full border-collapse space-y-1\",\n                                                        head_row: \"flex w-full\",\n                                                        head_cell: \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] text-center\",\n                                                        row: \"flex w-full mt-2\",\n                                                        cell: \"flex-1 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n                                                        day: \"h-9 w-full p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\",\n                                                        day_range_end: \"day-range-end\",\n                                                        day_selected: \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n                                                        day_today: \"bg-accent text-accent-foreground\",\n                                                        day_outside: \"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30\",\n                                                        day_disabled: \"text-muted-foreground opacity-50\",\n                                                        day_range_middle: \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n                                                        day_hidden: \"invisible\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatDateBR)(selectedDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors cursor-pointer space-y-3 sm:space-y-0\",\n                                                            onClick: ()=>handleAppointmentClick(appointment),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.start_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_8__.formatTimeBR)(appointment.end_time)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && 'Agendado',\n                                                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                                                appointment.status === 'completed' && 'Concluído',\n                                                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium whitespace-nowrap\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                appointment.price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            appointments: appointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"O/T8D1XACkCTyFGIp2fgJtnt9E4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});