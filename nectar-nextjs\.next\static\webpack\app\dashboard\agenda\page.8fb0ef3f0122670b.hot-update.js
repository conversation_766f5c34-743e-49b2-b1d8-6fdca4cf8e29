"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await fetch(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const data = await response.json();\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            initialData.start_time = selectInfo.start.toISOString().slice(0, 16);\n            initialData.end_time = selectInfo.end.toISOString().slice(0, 16);\n        } else if (selectedDate) {\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = startTime.toISOString().slice(0, 16);\n            initialData.end_time = endTime.toISOString().slice(0, 16);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // Handle appointment click - could open edit form\n        console.log('Appointment clicked:', appointment);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) throw new Error('Failed to create appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error creating appointment:', error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 372,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    appointmentCounts: appointmentCounts,\n                                                    clinicSettings: clinicSettings || undefined,\n                                                    appointments: allAppointments,\n                                                    className: \"rounded-md border-0 shadow-none w-full\",\n                                                    classNames: {\n                                                        months: \"flex flex-col space-y-4 w-full\",\n                                                        month: \"space-y-4 w-full\",\n                                                        caption: \"flex justify-center pt-1 relative items-center\",\n                                                        caption_label: \"text-sm font-medium\",\n                                                        nav: \"space-x-1 flex items-center\",\n                                                        nav_button: \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-accent rounded-md\",\n                                                        nav_button_previous: \"absolute left-1\",\n                                                        nav_button_next: \"absolute right-1\",\n                                                        table: \"w-full border-collapse space-y-1\",\n                                                        head_row: \"flex w-full\",\n                                                        head_cell: \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] text-center\",\n                                                        row: \"flex w-full mt-2\",\n                                                        cell: \"flex-1 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n                                                        day: \"h-9 w-full p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\",\n                                                        day_range_end: \"day-range-end\",\n                                                        day_selected: \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n                                                        day_today: \"bg-accent text-accent-foreground\",\n                                                        day_outside: \"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30\",\n                                                        day_disabled: \"text-muted-foreground opacity-50\",\n                                                        day_range_middle: \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n                                                        day_hidden: \"invisible\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 369,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(selectedDate, 'dd/MM/yyyy', {\n                                                                        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_16__.ptBR\n                                                                    })\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 429,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 430,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors cursor-pointer space-y-3 sm:space-y-0\",\n                                                            onClick: ()=>handleAppointmentClick(appointment),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(appointment.start_time), 'HH:mm')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(appointment.end_time), 'HH:mm')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 450,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 446,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 455,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 456,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 460,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 445,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && 'Agendado',\n                                                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                                                appointment.status === 'completed' && 'Concluído',\n                                                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 467,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium whitespace-nowrap\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                appointment.price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 483,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 466,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 415,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            appointments: appointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 510,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 313,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"O/T8D1XACkCTyFGIp2fgJtnt9E4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});