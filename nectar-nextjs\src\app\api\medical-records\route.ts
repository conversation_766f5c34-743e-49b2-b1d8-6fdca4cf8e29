import { NextRequest } from 'next/server'
import { withAuth, createApiResponse, handleApiError } from '@/lib/api-utils'

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const appointmentId = searchParams.get('appointment_id')
      const patientId = searchParams.get('patient_id')

      let query = supabase
        .from('medical_records')
        .select(`
          *,
          appointments!inner(patient_id),
          users(name)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (appointmentId) {
        query = query.eq('appointment_id', appointmentId)
      }

      if (patientId) {
        query = query.eq('appointments.patient_id', patientId)
      }

      const { data: records, error } = await query

      if (error) {
        return handleApiError(error)
      }

      // Format the response to include creator name
      const formattedRecords = records?.map(record => ({
        ...record,
        created_by_name: record.users?.name || 'Sistema'
      })) || []

      return createApiResponse(formattedRecords)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { appointment_id, patient_id, notes } = body

      if (!appointment_id || !notes?.trim()) {
        return handleApiError(new Error('appointment_id and notes are required'))
      }

      // Verify the appointment belongs to the user
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .select('id, patient_id')
        .eq('id', appointment_id)
        .eq('user_id', userId)
        .single()

      if (appointmentError || !appointment) {
        return handleApiError(new Error('Appointment not found or access denied'))
      }

      const recordData = {
        appointment_id,
        patient_id: patient_id || appointment.patient_id,
        user_id: userId,
        notes: notes.trim(),
        created_at: new Date().toISOString()
      }

      const { data: record, error } = await supabase
        .from('medical_records')
        .insert(recordData)
        .select(`
          *,
          users(name)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      // Format the response
      const formattedRecord = {
        ...record,
        created_by_name: record.users?.name || 'Sistema'
      }

      return createApiResponse(formattedRecord, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function PUT(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { id, notes } = body

      if (!id || !notes?.trim()) {
        return handleApiError(new Error('id and notes are required'))
      }

      const { data: record, error } = await supabase
        .from('medical_records')
        .update({
          notes: notes.trim(),
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .eq('user_id', userId)
        .select(`
          *,
          users(name)
        `)
        .single()

      if (error) {
        return handleApiError(error)
      }

      // Format the response
      const formattedRecord = {
        ...record,
        created_by_name: record.users?.name || 'Sistema'
      }

      return createApiResponse(formattedRecord)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function DELETE(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const id = searchParams.get('id')

      if (!id) {
        return handleApiError(new Error('id is required'))
      }

      const { error } = await supabase
        .from('medical_records')
        .delete()
        .eq('id', id)
        .eq('user_id', userId)

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse({ message: 'Medical record deleted successfully' })
    } catch (error) {
      return handleApiError(error)
    }
  })
}
