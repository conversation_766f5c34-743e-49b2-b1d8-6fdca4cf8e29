"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/ui/calendar.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/calendar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Calendar: () => (/* binding */ Calendar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_day_picker__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-day-picker */ \"(app-pages-browser)/./node_modules/react-day-picker/dist/esm/DayPicker.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isSameDay,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/startOfDay.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isSameDay,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isAfter.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isSameDay,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isSameDay.js\");\n/* harmony import */ var _barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isAfter,isSameDay,startOfDay!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Calendar(param) {\n    let { className, classNames, showOutsideDays = true, appointmentCounts = {}, clinicSettings, appointments = [], ...props } = param;\n    _s();\n    const modifiers = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"Calendar.useMemo[modifiers]\": ()=>{\n            const hasAppointments = [];\n            const hasAvailability = [];\n            const today = (0,_barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_4__.startOfDay)(new Date());\n            Object.keys(appointmentCounts).forEach({\n                \"Calendar.useMemo[modifiers]\": (dateKey)=>{\n                    if (appointmentCounts[dateKey] > 0) {\n                        hasAppointments.push(new Date(dateKey));\n                    }\n                }\n            }[\"Calendar.useMemo[modifiers]\"]);\n            // Calculate availability for future dates only\n            if (clinicSettings) {\n                const calculateAvailableSlots = {\n                    \"Calendar.useMemo[modifiers].calculateAvailableSlots\": (date)=>{\n                        if (!(0,_barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_5__.isAfter)(date, today) && !(0,_barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_6__.isSameDay)(date, today)) return 0;\n                        const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // Convert Sunday from 0 to 7\n                        // Check if it's a working day\n                        if (!clinicSettings.working_days.includes(dayOfWeek)) {\n                            if (!clinicSettings.allow_weekend_appointments) return 0;\n                        }\n                        // Calculate total available slots\n                        const [startHour, startMinute] = clinicSettings.working_hours_start.split(':').map(Number);\n                        const [endHour, endMinute] = clinicSettings.working_hours_end.split(':').map(Number);\n                        const startMinutes = startHour * 60 + startMinute;\n                        const endMinutes = endHour * 60 + endMinute;\n                        const totalMinutes = endMinutes - startMinutes;\n                        const totalSlots = Math.floor(totalMinutes / clinicSettings.appointment_duration_minutes);\n                        // Count existing appointments for this date\n                        const dateStr = (0,_barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(date, 'yyyy-MM-dd');\n                        const dayAppointments = appointments.filter({\n                            \"Calendar.useMemo[modifiers].calculateAvailableSlots.dayAppointments\": (apt)=>(0,_barrel_optimize_names_format_isAfter_isSameDay_startOfDay_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(new Date(apt.start_time), 'yyyy-MM-dd') === dateStr\n                        }[\"Calendar.useMemo[modifiers].calculateAvailableSlots.dayAppointments\"]);\n                        const occupiedSlots = dayAppointments.length;\n                        return Math.max(0, totalSlots - occupiedSlots);\n                    }\n                }[\"Calendar.useMemo[modifiers].calculateAvailableSlots\"];\n                // Check availability for the next 60 days\n                for(let i = 0; i < 60; i++){\n                    const checkDate = new Date(today);\n                    checkDate.setDate(checkDate.getDate() + i);\n                    if (calculateAvailableSlots(checkDate) > 0) {\n                        hasAvailability.push(checkDate);\n                    }\n                }\n            }\n            return {\n                hasAppointments,\n                hasAvailability\n            };\n        }\n    }[\"Calendar.useMemo[modifiers]\"], [\n        appointmentCounts,\n        clinicSettings,\n        appointments\n    ]);\n    const modifiersClassNames = {\n        hasAppointments: \"relative after:absolute after:bottom-1 after:left-1/2 after:transform after:-translate-x-1/2 after:w-1 after:h-1 after:bg-primary after:rounded-full\",\n        hasAvailability: \"bg-green-50 hover:bg-green-100 border-green-200\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_day_picker__WEBPACK_IMPORTED_MODULE_8__.DayPicker, {\n        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_9__.ptBR,\n        showOutsideDays: showOutsideDays,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-3\", className),\n        modifiers: modifiers,\n        modifiersClassNames: modifiersClassNames,\n        classNames: {\n            months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\n            month: \"space-y-4\",\n            caption: \"flex justify-center pt-1 relative items-center\",\n            caption_label: \"text-sm font-medium\",\n            nav: \"space-x-1 flex items-center\",\n            nav_button: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((0,_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.buttonVariants)({\n                variant: \"outline\"\n            }), \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"),\n            nav_button_previous: \"absolute left-1\",\n            nav_button_next: \"absolute right-1\",\n            table: \"w-full border-collapse space-y-1\",\n            head_row: \"flex\",\n            head_cell: \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\n            row: \"flex w-full mt-2\",\n            cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n            day: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((0,_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.buttonVariants)({\n                variant: \"ghost\"\n            }), \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"),\n            day_range_end: \"day-range-end\",\n            day_selected: \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n            day_today: \"bg-accent text-accent-foreground\",\n            day_outside: \"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30\",\n            day_disabled: \"text-muted-foreground opacity-50\",\n            day_range_middle: \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n            day_hidden: \"invisible\",\n            ...classNames\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\ui\\\\calendar.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n_s(Calendar, \"FF9rzgz1rJlSlBXZNXKypIfB2ok=\");\n_c = Calendar;\nCalendar.displayName = \"Calendar\";\n\nvar _c;\n$RefreshReg$(_c, \"Calendar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/calendar.tsx\n"));

/***/ })

});