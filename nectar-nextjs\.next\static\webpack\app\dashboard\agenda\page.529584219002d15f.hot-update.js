"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/agenda/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/agenda/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Grid3X3,Plus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/pt-BR.js\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* harmony import */ var _components_FullCalendarView__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/FullCalendarView */ \"(app-pages-browser)/./src/components/FullCalendarView.tsx\");\n/* harmony import */ var _components_AppointmentForm__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AppointmentForm */ \"(app-pages-browser)/./src/components/AppointmentForm.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst AgendaPage = ()=>{\n    _s();\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allAppointments, setAllAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [patients, setPatients] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [healthcareProfessionals, setHealthcareProfessionals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [procedures, setProcedures] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [clinicSettings, setClinicSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [appointmentFormOpen, setAppointmentFormOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('calendar');\n    const [appointmentFormData, setAppointmentFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AgendaPage.useEffect\": ()=>{\n            fetchAppointments();\n            fetchAllAppointments();\n            fetchPatients();\n            fetchHealthcareProfessionals();\n            fetchProcedures();\n            fetchClinicSettings();\n        }\n    }[\"AgendaPage.useEffect\"], [\n        selectedDate\n    ]);\n    const fetchAppointments = async ()=>{\n        try {\n            setLoading(true);\n            const dateStr = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(selectedDate, 'yyyy-MM-dd');\n            const response = await fetch(\"/api/appointments?date=\".concat(dateStr));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const data = await response.json();\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n            toast({\n                title: \"Erro ao carregar consultas\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchPatients = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/patients');\n            if (!response.ok) throw new Error('Failed to fetch patients');\n            const result = await response.json();\n            console.log('Patients API response:', result);\n            const data = result.data || result;\n            setPatients(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching patients:', error);\n            setPatients([]);\n            toast({\n                title: \"Erro ao carregar pacientes\",\n                description: error instanceof Error ? error.message : 'Ocorreu um erro inesperado',\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchHealthcareProfessionals = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/healthcare-professionals');\n            if (!response.ok) throw new Error('Failed to fetch healthcare professionals');\n            const result = await response.json();\n            const data = result.data || result;\n            setHealthcareProfessionals(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching healthcare professionals:', error);\n            setHealthcareProfessionals([]);\n        }\n    };\n    const fetchProcedures = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/procedures');\n            if (!response.ok) throw new Error('Failed to fetch procedures');\n            const result = await response.json();\n            const data = result.data || result;\n            setProcedures(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching procedures:', error);\n            setProcedures([]);\n        }\n    };\n    const fetchAllAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/appointments');\n            if (!response.ok) throw new Error('Failed to fetch all appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAllAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching all appointments:', error);\n            setAllAppointments([]);\n        }\n    };\n    const fetchClinicSettings = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/clinic-settings');\n            if (!response.ok) throw new Error('Failed to fetch clinic settings');\n            const result = await response.json();\n            const data = result.data || result;\n            setClinicSettings(data);\n        } catch (error) {\n            console.error('Error fetching clinic settings:', error);\n            // Set default settings if fetch fails\n            setClinicSettings({\n                working_hours_start: '08:00',\n                working_hours_end: '18:00',\n                working_days: [\n                    1,\n                    2,\n                    3,\n                    4,\n                    5\n                ],\n                appointment_duration_minutes: 30,\n                allow_weekend_appointments: false\n            });\n        }\n    };\n    const handleAppointmentCreate = (selectInfo)=>{\n        const initialData = {};\n        if (selectInfo) {\n            initialData.start_time = selectInfo.start.toISOString().slice(0, 16);\n            initialData.end_time = selectInfo.end.toISOString().slice(0, 16);\n        } else if (selectedDate) {\n            const startTime = new Date(selectedDate);\n            startTime.setHours(9, 0, 0, 0);\n            const endTime = new Date(startTime);\n            endTime.setMinutes(endTime.getMinutes() + 30);\n            initialData.start_time = startTime.toISOString().slice(0, 16);\n            initialData.end_time = endTime.toISOString().slice(0, 16);\n        }\n        setAppointmentFormData(initialData);\n        setAppointmentFormOpen(true);\n    };\n    const handleAppointmentClick = (appointment)=>{\n        // Handle appointment click - could open edit form\n        console.log('Appointment clicked:', appointment);\n    };\n    const handleAppointmentUpdate = async (appointmentId, newStart, newEnd)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    start_time: newStart.toISOString(),\n                    end_time: newEnd.toISOString()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            throw error;\n        }\n    };\n    const handleAppointmentSubmit = async (data)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)('/api/appointments', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) throw new Error('Failed to create appointment');\n            // Refresh appointments\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error creating appointment:', error);\n            throw error;\n        }\n    };\n    // Calculate appointment counts for calendar indicators\n    const appointmentCounts = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"AgendaPage.useMemo[appointmentCounts]\": ()=>{\n            const counts = {};\n            appointments.forEach({\n                \"AgendaPage.useMemo[appointmentCounts]\": (appointment)=>{\n                    const date = new Date(appointment.start_time).toISOString().split('T')[0];\n                    counts[date] = (counts[date] || 0) + 1;\n                }\n            }[\"AgendaPage.useMemo[appointmentCounts]\"]);\n            return counts;\n        }\n    }[\"AgendaPage.useMemo[appointmentCounts]\"], [\n        appointments\n    ]);\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAppointment = async (appointmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir esta consulta?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_8__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete appointment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta excluída com sucesso.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error deleting appointment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-foreground\",\n                                children: \"Agenda\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"Gerencie suas consultas e hor\\xe1rios\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>handleAppointmentCreate(),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Nova Consulta\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 313,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: currentView,\n                onValueChange: (value)=>{\n                    // Prevent tab switching during loading to avoid race conditions\n                    if (loading) {\n                        toast({\n                            title: \"Aguarde\",\n                            description: \"Aguarde o carregamento dos dados antes de trocar de aba.\",\n                            variant: \"default\"\n                        });\n                        return;\n                    }\n                    setCurrentView(value);\n                },\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"calendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Calend\\xe1rio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Cal.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"fullcalendar\",\n                                className: \"flex items-center gap-2 text-xs sm:text-sm \".concat(loading ? 'opacity-50 cursor-not-allowed' : ''),\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"hidden xs:inline\",\n                                        children: \"Agenda Completa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"xs:hidden\",\n                                        children: \"Agenda\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-3 w-3 border-b-2 border-current ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"calendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center text-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5 text-primary\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \"Calend\\xe1rio\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_2__.Calendar, {\n                                                    mode: \"single\",\n                                                    selected: selectedDate,\n                                                    onSelect: (date)=>date && setSelectedDate(date),\n                                                    appointmentCounts: appointmentCounts,\n                                                    className: \"rounded-md border-0 shadow-none w-full\",\n                                                    classNames: {\n                                                        months: \"flex flex-col space-y-4 w-full\",\n                                                        month: \"space-y-4 w-full\",\n                                                        caption: \"flex justify-center pt-1 relative items-center\",\n                                                        caption_label: \"text-sm font-medium\",\n                                                        nav: \"space-x-1 flex items-center\",\n                                                        nav_button: \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 hover:bg-accent rounded-md\",\n                                                        nav_button_previous: \"absolute left-1\",\n                                                        nav_button_next: \"absolute right-1\",\n                                                        table: \"w-full border-collapse space-y-1\",\n                                                        head_row: \"flex w-full\",\n                                                        head_cell: \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] text-center\",\n                                                        row: \"flex w-full mt-2\",\n                                                        cell: \"flex-1 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n                                                        day: \"h-9 w-full p-0 font-normal aria-selected:opacity-100 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground\",\n                                                        day_range_end: \"day-range-end\",\n                                                        day_selected: \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n                                                        day_today: \"bg-accent text-accent-foreground\",\n                                                        day_outside: \"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30\",\n                                                        day_disabled: \"text-muted-foreground opacity-50\",\n                                                        day_range_middle: \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n                                                        day_hidden: \"invisible\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center text-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"hidden sm:inline\",\n                                                                children: [\n                                                                    \"Consultas - \",\n                                                                    (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(selectedDate, 'dd/MM/yyyy', {\n                                                                        locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_16__.ptBR\n                                                                    })\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"sm:hidden\",\n                                                                children: \"Consultas\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            appointments.length,\n                                                            \" consulta(s) agendada(s) para este dia\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50 animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Carregando consultas...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, undefined) : appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Grid3X3_Plus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Nenhuma consulta agendada para este dia\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 21\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-lg border bg-card/50 hover:bg-card transition-colors cursor-pointer space-y-3 sm:space-y-0\",\n                                                            onClick: ()=>handleAppointmentClick(appointment),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-4 min-w-0 flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col items-center flex-shrink-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-medium\",\n                                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(appointment.start_time), 'HH:mm')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 444,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-muted-foreground\",\n                                                                                    children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(appointment.end_time), 'HH:mm')\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 447,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 443,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 min-w-0\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                    className: \"font-medium truncate\",\n                                                                                    children: appointment.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 452,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-sm text-muted-foreground truncate\",\n                                                                                    children: appointment.patient_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 453,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                    className: \"text-xs text-muted-foreground truncate hidden sm:block\",\n                                                                                    children: appointment.healthcare_professional_name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                                    lineNumber: 457,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 flex-shrink-0\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                            variant: appointment.status === 'confirmed' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                            className: \"text-xs\",\n                                                                            children: [\n                                                                                appointment.status === 'scheduled' && 'Agendado',\n                                                                                appointment.status === 'confirmed' && 'Confirmado',\n                                                                                appointment.status === 'completed' && 'Concluído',\n                                                                                appointment.status === 'cancelled' && 'Cancelado',\n                                                                                appointment.status === 'in_progress' && 'Em Andamento'\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 464,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium whitespace-nowrap\",\n                                                                            children: [\n                                                                                \"R$ \",\n                                                                                appointment.price.toFixed(2)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, appointment.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                                    lineNumber: 411,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 367,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"fullcalendar\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FullCalendarView__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            appointments: appointments,\n                            healthcareProfessionals: healthcareProfessionals,\n                            onAppointmentCreate: handleAppointmentCreate,\n                            onAppointmentClick: handleAppointmentClick,\n                            onAppointmentUpdate: handleAppointmentUpdate,\n                            loading: loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                        lineNumber: 495,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AppointmentForm__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                open: appointmentFormOpen,\n                onOpenChange: setAppointmentFormOpen,\n                patients: patients,\n                healthcareProfessionals: healthcareProfessionals,\n                procedures: procedures,\n                initialData: appointmentFormData,\n                onSubmit: handleAppointmentSubmit,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n                lineNumber: 507,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\agenda\\\\page.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AgendaPage, \"O/T8D1XACkCTyFGIp2fgJtnt9E4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = AgendaPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AgendaPage);\nvar _c;\n$RefreshReg$(_c, \"AgendaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/agenda/page.tsx\n"));

/***/ })

});