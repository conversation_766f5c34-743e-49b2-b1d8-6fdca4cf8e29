# Guia de Desenvolvimento - Nectar

Este documento contém informações técnicas para desenvolvedores que trabalham no projeto Nectar.

## 🏗️ Arquitetura

### Estrutura de Pastas

```
src/
├── app/                    # App Router do Next.js
│   ├── dashboard/         # Páginas do dashboard
│   ├── api/              # API Routes
│   └── globals.css       # Estilos globais
├── components/           # Componentes React
│   ├── ui/              # Componentes base (shadcn/ui)
│   └── ...              # Componentes específicos
├── hooks/               # Custom hooks
├── lib/                 # Utilitários e configurações
│   ├── supabase/       # Cliente Supabase
│   ├── validations.ts  # Esquemas Zod
│   └── utils.ts        # Funções utilitárias
├── types/              # Definições TypeScript
└── __tests__/          # Testes
```

### Padrões de Código

#### Componentes React
```typescript
// Sempre use TypeScript
interface ComponentProps {
  title: string;
  optional?: boolean;
}

const Component: React.FC<ComponentProps> = ({ title, optional = false }) => {
  return <div>{title}</div>;
};

export default Component;
```

#### API Routes
```typescript
// Use withAuth ou withAuthAndPermission
import { withAuthAndPermission } from '@/lib/api-utils';

export async function GET(request: NextRequest) {
  return withAuthAndPermission(request, 'resource', 'read', async (userId, supabase) => {
    // Lógica da API
  });
}
```

#### Hooks Customizados
```typescript
// Sempre retorne um objeto com propriedades nomeadas
export function useCustomHook() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  return { data, loading, refetch: () => {} };
}
```

## 🔧 Configuração do Ambiente

### Variáveis de Ambiente

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Desenvolvimento
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Banco de Dados

#### Estrutura de Tabelas

**patients**
- `id` (UUID, PK)
- `user_id` (UUID, FK)
- `name` (TEXT)
- `email` (TEXT)
- `phone` (TEXT)
- `birth_date` (DATE)
- `cpf` (TEXT)
- `address` (TEXT)
- `notes` (TEXT)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

**appointments**
- `id` (UUID, PK)
- `user_id` (UUID, FK)
- `patient_id` (UUID, FK)
- `healthcare_professional_id` (UUID, FK)
- `title` (TEXT)
- `description` (TEXT)
- `start_time` (TIMESTAMP)
- `end_time` (TIMESTAMP)
- `type` (TEXT)
- `status` (TEXT)
- `price` (DECIMAL)
- `notes` (TEXT)
- `recurrence_rule` (TEXT)
- `recurrence_end_date` (DATE)
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### Políticas RLS

Todas as tabelas têm políticas RLS que garantem:
- Usuários só acessam seus próprios dados
- Admins podem gerenciar dados da organização
- Logs de auditoria para mudanças críticas

## 🧪 Testes

### Estrutura de Testes

```
__tests__/
├── components/         # Testes de componentes
├── hooks/             # Testes de hooks
├── lib/               # Testes de utilitários
├── api/               # Testes de API
└── integration/       # Testes de integração
```

### Convenções de Teste

#### Componentes
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import Component from '@/components/Component';

describe('Component', () => {
  it('should render correctly', () => {
    render(<Component title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('should handle user interactions', () => {
    const mockFn = jest.fn();
    render(<Component onClick={mockFn} />);
    
    fireEvent.click(screen.getByRole('button'));
    expect(mockFn).toHaveBeenCalled();
  });
});
```

#### APIs
```typescript
import { createMocks } from 'node-mocks-http';
import { GET } from '@/app/api/resource/route';

describe('/api/resource', () => {
  it('should return data for authenticated user', async () => {
    const { req } = createMocks({ method: 'GET' });
    const response = await GET(req as any);
    
    expect(response.status).toBe(200);
  });
});
```

### Mocks

#### Supabase
```typescript
jest.mock('@/lib/supabase/client', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      // ...
    })),
  })),
}));
```

#### Next.js Router
```typescript
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    // ...
  }),
}));
```

## 🔐 Segurança

### Autenticação

```typescript
// Cliente (browser)
import { createClient } from '@/lib/supabase/client';

const supabase = createClient();
const { data: { user } } = await supabase.auth.getUser();

// Servidor (API routes)
import { createClient } from '@/lib/supabase/server';

const supabase = await createClient();
const { data: { user } } = await supabase.auth.getUser();
```

### Permissões

```typescript
// Verificar permissão
import { hasPermission } from '@/lib/permissions';

const canCreate = await hasPermission(userId, 'appointments', 'create');

// Componente com gate
import { PermissionGate } from '@/hooks/usePermissions';

<PermissionGate resource="appointments" action="create">
  <CreateButton />
</PermissionGate>
```

### Validação

```typescript
// Sempre validar entrada
import { patientSchema } from '@/lib/validations';

const result = patientSchema.safeParse(data);
if (!result.success) {
  return { error: result.error.issues };
}
```

## 📱 Responsividade

### Breakpoints

```typescript
// Tailwind breakpoints
const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};
```

### Padrões Responsivos

```tsx
// Layout responsivo
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
  {/* Conteúdo */}
</div>

// Texto responsivo
<h1 className="text-lg sm:text-xl md:text-2xl lg:text-3xl">
  Título
</h1>

// Espaçamento responsivo
<div className="p-4 sm:p-6 lg:p-8">
  {/* Conteúdo */}
</div>
```

### Mobile First

```tsx
// Sempre comece com mobile
<div className="flex flex-col sm:flex-row">
  <div className="w-full sm:w-1/2">
    {/* Conteúdo */}
  </div>
</div>
```

## 🚀 Performance

### Otimizações

#### Lazy Loading
```typescript
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <Skeleton />,
});
```

#### Memoização
```typescript
import { memo, useMemo, useCallback } from 'react';

const Component = memo(({ data }) => {
  const processedData = useMemo(() => {
    return expensiveOperation(data);
  }, [data]);

  const handleClick = useCallback(() => {
    // Handler
  }, []);

  return <div>{processedData}</div>;
});
```

#### Bundle Analysis
```bash
npm run build
npx @next/bundle-analyzer
```

## 🐛 Debug

### Logs
```typescript
// Desenvolvimento
console.log('Debug info:', data);

// Produção
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', data);
}
```

### Error Boundary
```tsx
import ErrorBoundary from '@/components/ErrorBoundary';

<ErrorBoundary>
  <Component />
</ErrorBoundary>
```

### Supabase Debug
```typescript
// Habilitar logs do Supabase
const supabase = createClient(url, key, {
  auth: {
    debug: process.env.NODE_ENV === 'development',
  },
});
```

## 📦 Deploy

### Build
```bash
npm run build
npm run start
```

### Variáveis de Produção
```env
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

### Checklist de Deploy
- [ ] Testes passando
- [ ] Build sem erros
- [ ] Variáveis de ambiente configuradas
- [ ] Migrações do banco aplicadas
- [ ] Políticas RLS ativas
- [ ] SSL configurado
- [ ] Monitoramento ativo

## 🔄 Workflow

### Git Flow
```bash
# Feature branch
git checkout -b feature/new-feature
git commit -m "feat: add new feature"
git push origin feature/new-feature

# Pull request
# Code review
# Merge to main
```

### Commits Convencionais
```
feat: nova funcionalidade
fix: correção de bug
docs: documentação
style: formatação
refactor: refatoração
test: testes
chore: manutenção
```

---

Para dúvidas técnicas, consulte a documentação ou abra uma issue no repositório.
