"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/FullCalendarView.tsx":
/*!*********************************************!*\
  !*** ./src/components/FullCalendarView.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fullcalendar/react */ \"(app-pages-browser)/./node_modules/@fullcalendar/react/dist/index.js\");\n/* harmony import */ var _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fullcalendar/daygrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/daygrid/index.js\");\n/* harmony import */ var _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fullcalendar/timegrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/timegrid/index.js\");\n/* harmony import */ var _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fullcalendar/interaction */ \"(app-pages-browser)/./node_modules/@fullcalendar/interaction/index.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst FullCalendarView = (param)=>{\n    let { appointments, healthcareProfessionals, onAppointmentCreate, onAppointmentClick, onAppointmentUpdate, loading = false } = param;\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedProfessional, setSelectedProfessional] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('timeGridWeek');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullCalendarView.useEffect\": ()=>{\n            const checkMobile = {\n                \"FullCalendarView.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    // Auto-switch to day view on mobile\n                    if (window.innerWidth < 768 && currentView === 'timeGridWeek') {\n                        setCurrentView('timeGridDay');\n                    }\n                }\n            }[\"FullCalendarView.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"FullCalendarView.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"FullCalendarView.useEffect\"];\n        }\n    }[\"FullCalendarView.useEffect\"], [\n        currentView\n    ]);\n    // Filter appointments by selected healthcare professional\n    const filteredAppointments = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[filteredAppointments]\": ()=>{\n            if (selectedProfessional === 'all') {\n                return appointments;\n            }\n            return appointments.filter({\n                \"FullCalendarView.useMemo[filteredAppointments]\": (apt)=>apt.healthcare_professional_id === selectedProfessional\n            }[\"FullCalendarView.useMemo[filteredAppointments]\"]);\n        }\n    }[\"FullCalendarView.useMemo[filteredAppointments]\"], [\n        appointments,\n        selectedProfessional\n    ]);\n    // Convert appointments to FullCalendar events\n    const events = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[events]\": ()=>{\n            return filteredAppointments.map({\n                \"FullCalendarView.useMemo[events]\": (appointment)=>({\n                        id: appointment.id,\n                        title: appointment.title,\n                        start: appointment.start_time,\n                        end: appointment.end_time,\n                        backgroundColor: getStatusColor(appointment.status),\n                        borderColor: getStatusColor(appointment.status),\n                        textColor: '#ffffff',\n                        extendedProps: {\n                            appointment,\n                            description: appointment.description,\n                            patientName: appointment.patient_name,\n                            professionalName: appointment.healthcare_professional_name,\n                            status: appointment.status,\n                            type: appointment.type,\n                            price: appointment.price\n                        }\n                    })\n            }[\"FullCalendarView.useMemo[events]\"]);\n        }\n    }[\"FullCalendarView.useMemo[events]\"], [\n        filteredAppointments\n    ]);\n    const getStatusColor = (status)=>{\n        const colors = {\n            'scheduled': '#3b82f6',\n            'confirmed': '#10b981',\n            'in_progress': '#f59e0b',\n            'completed': '#6b7280',\n            'cancelled': '#ef4444'\n        };\n        return colors[status] || '#6b7280';\n    };\n    const handleDateSelect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleDateSelect]\": (selectInfo)=>{\n            if (onAppointmentCreate) {\n                onAppointmentCreate(selectInfo);\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleDateSelect]\"], [\n        onAppointmentCreate\n    ]);\n    const handleEventClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleEventClick]\": (clickInfo)=>{\n            const appointment = clickInfo.event.extendedProps.appointment;\n            if (onAppointmentClick && appointment) {\n                onAppointmentClick(appointment);\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleEventClick]\"], [\n        onAppointmentClick\n    ]);\n    const handleEventDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FullCalendarView.useCallback[handleEventDrop]\": async (dropInfo)=>{\n            try {\n                const appointmentId = dropInfo.event.id;\n                const newStart = dropInfo.event.start;\n                const newEnd = dropInfo.event.end;\n                if (!appointmentId || !newStart || !newEnd || !onAppointmentUpdate) {\n                    dropInfo.revert();\n                    return;\n                }\n                await onAppointmentUpdate(appointmentId, newStart, newEnd);\n                toast({\n                    title: \"Sucesso!\",\n                    description: \"Consulta reagendada com sucesso.\"\n                });\n            } catch (error) {\n                console.error('Error updating appointment:', error);\n                dropInfo.revert();\n                toast({\n                    title: \"Erro\",\n                    description: \"Erro ao reagendar consulta.\",\n                    variant: \"destructive\"\n                });\n            }\n        }\n    }[\"FullCalendarView.useCallback[handleEventDrop]\"], [\n        onAppointmentUpdate,\n        toast\n    ]);\n    const handleViewChange = (view)=>{\n        var _calendarRef_current;\n        setCurrentView(view);\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.changeView(view);\n        }\n    };\n    const goToToday = ()=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.today();\n        }\n    };\n    const navigateCalendar = (direction)=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            if (direction === 'prev') {\n                calendarApi.prev();\n            } else {\n                calendarApi.next();\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Agenda Completa\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-between sm:items-center sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                            value: selectedProfessional,\n                                            onValueChange: setSelectedProfessional,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                    className: \"w-full sm:w-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                        placeholder: \"Filtrar por profissional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"Todos os profissionais\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: professional.id,\n                                                                children: [\n                                                                    professional.name,\n                                                                    professional.specialty && \" - \".concat(professional.specialty)\n                                                                ]\n                                                            }, professional.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col xs:flex-row gap-2 xs:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'dayGridMonth' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('dayGridMonth'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'M' : 'Mês'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridWeek' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>handleViewChange('timeGridWeek'),\n                                                    children: \"Semana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridDay' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('timeGridDay'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'D' : 'Dia'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('prev'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"‹\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: goToToday,\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"Hoje\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('next'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"›\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fullcalendar-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ref: calendarRef,\n                        plugins: [\n                            _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                            _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        ],\n                        initialView: isMobile ? \"timeGridDay\" : \"timeGridWeek\",\n                        headerToolbar: false,\n                        height: \"auto\",\n                        contentHeight: isMobile ? 400 : \"auto\",\n                        events: events || [],\n                        selectable: true,\n                        selectMirror: true,\n                        editable: !isMobile,\n                        droppable: !isMobile,\n                        eventResizableFromStart: !isMobile,\n                        select: handleDateSelect,\n                        eventClick: handleEventClick,\n                        eventDrop: handleEventDrop,\n                        slotMinTime: \"06:00:00\",\n                        slotMaxTime: \"22:00:00\",\n                        slotDuration: \"00:30:00\",\n                        slotLabelInterval: isMobile ? \"02:00:00\" : \"01:00:00\",\n                        allDaySlot: false,\n                        nowIndicator: true,\n                        businessHours: {\n                            daysOfWeek: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ],\n                            startTime: '08:00',\n                            endTime: '18:00'\n                        },\n                        eventDisplay: \"block\",\n                        dayMaxEvents: isMobile ? 3 : true,\n                        moreLinkClick: \"popover\",\n                        loading: loading,\n                        // Mobile-specific settings\n                        aspectRatio: isMobile ? 1.2 : 1.35,\n                        handleWindowResize: true,\n                        stickyHeaderDates: !isMobile,\n                        // Locale configuration\n                        locale: {\n                            code: 'pt-br',\n                            week: {\n                                dow: 0,\n                                doy: 4 // The week that contains Jan 4th is the first week of the year\n                            },\n                            buttonText: {\n                                today: 'Hoje',\n                                month: 'Mês',\n                                week: 'Semana',\n                                day: 'Dia',\n                                list: 'Lista'\n                            },\n                            weekText: 'Sm',\n                            allDayText: 'Todo o dia',\n                            moreLinkText: 'mais',\n                            noEventsText: 'Não há eventos para mostrar'\n                        },\n                        eventContent: (eventInfo)=>{\n                            var _eventInfo_event_extendedProps, _eventInfo_event_extendedProps1;\n                            const title = eventInfo.event.title || '';\n                            const patientName = (_eventInfo_event_extendedProps = eventInfo.event.extendedProps) === null || _eventInfo_event_extendedProps === void 0 ? void 0 : _eventInfo_event_extendedProps.patientName;\n                            const professionalName = (_eventInfo_event_extendedProps1 = eventInfo.event.extendedProps) === null || _eventInfo_event_extendedProps1 === void 0 ? void 0 : _eventInfo_event_extendedProps1.professionalName;\n                            const startTime = eventInfo.event.start;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1 \".concat(isMobile ? 'text-xs' : 'text-xs'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium truncate text-xs sm:text-sm\",\n                                        children: isMobile && title.length > 20 ? title.substring(0, 20) + '...' : title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    patientName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-90 truncate\",\n                                        children: patientName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    professionalName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: professionalName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    isMobile && startTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(startTime, 'HH:mm')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 21\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 17\n                            }, void 0);\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FullCalendarView, \"aM6QgluPdArICDtg/iNsMDjgKag=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = FullCalendarView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FullCalendarView);\nvar _c;\n$RefreshReg$(_c, \"FullCalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FullCalendarView.tsx\n"));

/***/ })

});