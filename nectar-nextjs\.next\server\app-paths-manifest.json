{"/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/clinic-settings/route": "app/api/clinic-settings/route.js", "/api/healthcare-professionals/route": "app/api/healthcare-professionals/route.js", "/api/user-roles/route": "app/api/user-roles/route.js", "/api/appointments/route": "app/api/appointments/route.js", "/api/procedures/route": "app/api/procedures/route.js", "/api/patients/route": "app/api/patients/route.js", "/_not-found/page": "app/_not-found/page.js", "/dashboard/agenda/page": "app/dashboard/agenda/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/configuracoes/page": "app/dashboard/configuracoes/page.js", "/dashboard/pacientes/page": "app/dashboard/pacientes/page.js"}