import { format, parseISO, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Utility functions for Brazilian date and time formatting
 */

/**
 * Format date to Brazilian format (DD/MM/YYYY)
 */
export function formatDateBR(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return format(dateObj, 'dd/MM/yyyy', { locale: ptBR });
  } catch {
    return '';
  }
}

/**
 * Format time to Brazilian format (HH:mm)
 */
export function formatTimeBR(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return format(dateObj, 'HH:mm', { locale: ptBR });
  } catch {
    return '';
  }
}

/**
 * Format date and time to Brazilian format (DD/MM/YYYY HH:mm)
 */
export function formatDateTimeBR(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return format(dateObj, 'dd/MM/yyyy HH:mm', { locale: ptBR });
  } catch {
    return '';
  }
}

/**
 * Format date to Brazilian format with day of week (Segunda, DD/MM/YYYY)
 */
export function formatDateWithDayBR(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return format(dateObj, 'EEEE, dd/MM/yyyy', { locale: ptBR });
  } catch {
    return '';
  }
}

/**
 * Format date for input fields (YYYY-MM-DD)
 */
export function formatDateForInput(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return format(dateObj, 'yyyy-MM-dd');
  } catch {
    return '';
  }
}

/**
 * Format datetime for input fields (YYYY-MM-DDTHH:mm)
 */
export function formatDateTimeForInput(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    return format(dateObj, "yyyy-MM-dd'T'HH:mm");
  } catch {
    return '';
  }
}

/**
 * Get relative time in Portuguese (hoje, ontem, amanhã, etc.)
 */
export function getRelativeTimeBR(date: Date | string): string {
  try {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    if (!isValid(dateObj)) return '';
    
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const dateStr = format(dateObj, 'yyyy-MM-dd');
    const todayStr = format(today, 'yyyy-MM-dd');
    const tomorrowStr = format(tomorrow, 'yyyy-MM-dd');
    const yesterdayStr = format(yesterday, 'yyyy-MM-dd');
    
    if (dateStr === todayStr) return 'Hoje';
    if (dateStr === tomorrowStr) return 'Amanhã';
    if (dateStr === yesterdayStr) return 'Ontem';
    
    return formatDateBR(dateObj);
  } catch {
    return '';
  }
}

/**
 * Parse Brazilian date format (DD/MM/YYYY) to Date object
 */
export function parseBRDate(dateStr: string): Date | null {
  try {
    const parts = dateStr.split('/');
    if (parts.length !== 3) return null;
    
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // Month is 0-indexed
    const year = parseInt(parts[2], 10);
    
    const date = new Date(year, month, day);
    if (!isValid(date)) return null;
    
    return date;
  } catch {
    return null;
  }
}

/**
 * Get appointment status in Portuguese
 */
export function getAppointmentStatusBR(status: string): string {
  const statusMap: Record<string, string> = {
    'scheduled': 'Agendado',
    'confirmed': 'Confirmado',
    'in_progress': 'Em Andamento',
    'completed': 'Concluído',
    'cancelled': 'Cancelado',
    'no_show': 'Faltou'
  };
  
  return statusMap[status] || status;
}

/**
 * Get appointment type in Portuguese
 */
export function getAppointmentTypeBR(type: string): string {
  const typeMap: Record<string, string> = {
    'consultation': 'Consulta',
    'return': 'Retorno',
    'teleconsultation': 'Teleconsulta',
    'procedure': 'Procedimento',
    'exam': 'Exame'
  };
  
  return typeMap[type] || type;
}
