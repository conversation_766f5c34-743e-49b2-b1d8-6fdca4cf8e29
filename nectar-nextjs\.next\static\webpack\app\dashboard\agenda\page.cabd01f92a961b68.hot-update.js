"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/components/FullCalendarView.tsx":
/*!*********************************************!*\
  !*** ./src/components/FullCalendarView.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @fullcalendar/react */ \"(app-pages-browser)/./node_modules/@fullcalendar/react/dist/index.js\");\n/* harmony import */ var _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fullcalendar/daygrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/daygrid/index.js\");\n/* harmony import */ var _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fullcalendar/timegrid */ \"(app-pages-browser)/./node_modules/@fullcalendar/timegrid/index.js\");\n/* harmony import */ var _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @fullcalendar/interaction */ \"(app-pages-browser)/./node_modules/@fullcalendar/interaction/index.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Filter!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst FullCalendarView = (param)=>{\n    let { appointments, healthcareProfessionals, onAppointmentCreate, onAppointmentClick, onAppointmentUpdate, loading = false } = param;\n    _s();\n    const calendarRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [selectedProfessional, setSelectedProfessional] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [currentView, setCurrentView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('timeGridWeek');\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FullCalendarView.useEffect\": ()=>{\n            const checkMobile = {\n                \"FullCalendarView.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                    // Auto-switch to day view on mobile\n                    if (window.innerWidth < 768 && currentView === 'timeGridWeek') {\n                        setCurrentView('timeGridDay');\n                    }\n                }\n            }[\"FullCalendarView.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"FullCalendarView.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"FullCalendarView.useEffect\"];\n        }\n    }[\"FullCalendarView.useEffect\"], [\n        currentView\n    ]);\n    // Filter appointments by selected healthcare professional\n    const filteredAppointments = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[filteredAppointments]\": ()=>{\n            if (selectedProfessional === 'all') {\n                return appointments;\n            }\n            return appointments.filter({\n                \"FullCalendarView.useMemo[filteredAppointments]\": (apt)=>apt.healthcare_professional_id === selectedProfessional\n            }[\"FullCalendarView.useMemo[filteredAppointments]\"]);\n        }\n    }[\"FullCalendarView.useMemo[filteredAppointments]\"], [\n        appointments,\n        selectedProfessional\n    ]);\n    // Convert appointments to FullCalendar events\n    const events = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"FullCalendarView.useMemo[events]\": ()=>{\n            return filteredAppointments.map({\n                \"FullCalendarView.useMemo[events]\": (appointment)=>({\n                        id: appointment.id,\n                        title: appointment.title,\n                        start: appointment.start_time,\n                        end: appointment.end_time,\n                        backgroundColor: getStatusColor(appointment.status),\n                        borderColor: getStatusColor(appointment.status),\n                        textColor: '#ffffff',\n                        extendedProps: {\n                            appointment,\n                            description: appointment.description,\n                            patientName: appointment.patient_name,\n                            professionalName: appointment.healthcare_professional_name,\n                            status: appointment.status,\n                            type: appointment.type,\n                            price: appointment.price\n                        }\n                    })\n            }[\"FullCalendarView.useMemo[events]\"]);\n        }\n    }[\"FullCalendarView.useMemo[events]\"], [\n        filteredAppointments\n    ]);\n    const getStatusColor = (status)=>{\n        const colors = {\n            'scheduled': '#3b82f6',\n            'confirmed': '#10b981',\n            'in_progress': '#f59e0b',\n            'completed': '#6b7280',\n            'cancelled': '#ef4444'\n        };\n        return colors[status] || '#6b7280';\n    };\n    const handleDateSelect = (selectInfo)=>{\n        onAppointmentCreate(selectInfo);\n    };\n    const handleEventClick = (clickInfo)=>{\n        const appointment = clickInfo.event.extendedProps.appointment;\n        onAppointmentClick(appointment);\n    };\n    const handleEventDrop = async (dropInfo)=>{\n        try {\n            const appointmentId = dropInfo.event.id;\n            const newStart = dropInfo.event.start;\n            const newEnd = dropInfo.event.end;\n            await onAppointmentUpdate(appointmentId, newStart, newEnd);\n            toast({\n                title: \"Sucesso!\",\n                description: \"Consulta reagendada com sucesso.\"\n            });\n        } catch (error) {\n            console.error('Error updating appointment:', error);\n            dropInfo.revert();\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao reagendar consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleViewChange = (view)=>{\n        var _calendarRef_current;\n        setCurrentView(view);\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.changeView(view);\n        }\n    };\n    const goToToday = ()=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            calendarApi.today();\n        }\n    };\n    const navigateCalendar = (direction)=>{\n        var _calendarRef_current;\n        const calendarApi = (_calendarRef_current = calendarRef.current) === null || _calendarRef_current === void 0 ? void 0 : _calendarRef_current.getApi();\n        if (calendarApi) {\n            if (direction === 'prev') {\n                calendarApi.prev();\n            } else {\n                calendarApi.next();\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5 text-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"Agenda Completa\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 sm:space-y-0 sm:flex sm:flex-wrap sm:justify-between sm:items-center sm:gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 min-w-0 flex-1 sm:flex-initial\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Filter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.Select, {\n                                            value: selectedProfessional,\n                                            onValueChange: setSelectedProfessional,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectTrigger, {\n                                                    className: \"w-full sm:w-[200px]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectValue, {\n                                                        placeholder: \"Filtrar por profissional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectContent, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                            value: \"all\",\n                                                            children: \"Todos os profissionais\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        healthcareProfessionals.filter((prof)=>prof.is_active).map((professional)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_4__.SelectItem, {\n                                                                value: professional.id,\n                                                                children: [\n                                                                    professional.name,\n                                                                    professional.specialty && \" - \".concat(professional.specialty)\n                                                                ]\n                                                            }, professional.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                                lineNumber: 198,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col xs:flex-row gap-2 xs:gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'dayGridMonth' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('dayGridMonth'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'M' : 'Mês'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridWeek' ? 'default' : 'outline',\n                                                    size: \"sm\",\n                                                    onClick: ()=>handleViewChange('timeGridWeek'),\n                                                    children: \"Semana\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: currentView === 'timeGridDay' ? 'default' : 'outline',\n                                                    size: isMobile ? 'sm' : 'sm',\n                                                    onClick: ()=>handleViewChange('timeGridDay'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: isMobile ? 'D' : 'Dia'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('prev'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"‹\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: goToToday,\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"Hoje\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    onClick: ()=>navigateCalendar('next'),\n                                                    className: \"flex-1 xs:flex-initial\",\n                                                    children: \"›\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fullcalendar-container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_fullcalendar_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        ref: calendarRef,\n                        plugins: [\n                            _fullcalendar_daygrid__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                            _fullcalendar_timegrid__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            _fullcalendar_interaction__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                        ],\n                        initialView: isMobile ? \"timeGridDay\" : \"timeGridWeek\",\n                        locale: \"pt-br\",\n                        headerToolbar: false,\n                        height: isMobile ? \"auto\" : \"auto\",\n                        contentHeight: isMobile ? 400 : \"auto\",\n                        events: events,\n                        selectable: true,\n                        selectMirror: true,\n                        editable: !isMobile,\n                        droppable: !isMobile,\n                        eventResizableFromStart: !isMobile,\n                        select: handleDateSelect,\n                        eventClick: handleEventClick,\n                        eventDrop: handleEventDrop,\n                        slotMinTime: \"06:00:00\",\n                        slotMaxTime: \"22:00:00\",\n                        slotDuration: \"00:30:00\",\n                        slotLabelInterval: isMobile ? \"02:00:00\" : \"01:00:00\",\n                        allDaySlot: false,\n                        nowIndicator: true,\n                        businessHours: {\n                            daysOfWeek: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ],\n                            startTime: '08:00',\n                            endTime: '18:00'\n                        },\n                        eventDisplay: \"block\",\n                        dayMaxEvents: isMobile ? 3 : true,\n                        moreLinkClick: isMobile ? \"popover\" : \"popover\",\n                        loading: loading,\n                        // Mobile-specific settings\n                        aspectRatio: isMobile ? 1.2 : 1.35,\n                        handleWindowResize: true,\n                        stickyHeaderDates: !isMobile,\n                        eventContent: (eventInfo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-1 \".concat(isMobile ? 'text-xs' : 'text-xs'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-medium truncate text-xs sm:text-sm\",\n                                        children: isMobile ? eventInfo.event.title.substring(0, 20) + (eventInfo.event.title.length > 20 ? '...' : '') : eventInfo.event.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, void 0),\n                                    eventInfo.event.extendedProps.patientName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-90 truncate\",\n                                        children: eventInfo.event.extendedProps.patientName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    eventInfo.event.extendedProps.professionalName && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: eventInfo.event.extendedProps.professionalName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-75 truncate\",\n                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_12__.format)(eventInfo.event.start, 'HH:mm')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 19\n                                    }, void 0)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 15\n                            }, void 0)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\components\\\\FullCalendarView.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FullCalendarView, \"rgl7eZlzybkSyKE4W9JAXltertM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = FullCalendarView;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FullCalendarView);\nvar _c;\n$RefreshReg$(_c, \"FullCalendarView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/FullCalendarView.tsx\n"));

/***/ })

});